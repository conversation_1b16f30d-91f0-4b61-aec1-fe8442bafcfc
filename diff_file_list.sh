#!/bin/bash

# Script to generate a unified diff file from a list of files between two Git references
# Usage: generate_diff.sh <branch1> <commit1> <branch2> <commit2> <file_list> <output_diff_file>

# Check if the correct number of arguments is provided
if [ $# -ne 6 ]; then
  echo "Usage: $0 <branch1> <commit1> <branch2> <commit2> <file_list> <output_diff_file>"
  exit 1
fi

# Assign arguments to variables
branch1="$1"
commit1="$2"
branch2="$3"
commit2="$4"
file_list="$5"
output_diff_file="$6"

# Check if the file_list exists
if [ ! -f "$file_list" ]; then
  echo "Error: File list '$file_list' does not exist."
  exit 1
fi

# Check if we're in a git repository
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
  echo "Error: Not in a git repository."
  exit 1
fi

# Validate git references
if ! git rev-parse --verify "$commit1" > /dev/null 2>&1; then
  echo "Error: Invalid commit '$commit1'"
  exit 1
fi

if ! git rev-parse --verify "$commit2" > /dev/null 2>&1; then
  echo "Error: Invalid commit '$commit2'"
  exit 1
fi

# Separator Line (with blank lines)
separator=$'\n____________________________________________________________________________________________________________________________________________\n\n'

# Generate the diff file
{
  while IFS= read -r file; do
    # Skip empty lines
    [ -z "$file" ] && continue
    
    # Check if file exists in both references
    if git ls-tree -r --name-only "$commit1" | grep -q "^$file$" && \
       git ls-tree -r --name-only "$commit2" | grep -q "^$file$"; then
      printf "%s" "$separator"  # Add separator before each diff
      git diff "$commit1" "$commit2" -- "$file" || true  # Ignore diff return code
    else
      echo "Warning: File '$file' does not exist in one or both references" >&2
    fi
  done < "$file_list"
} > "$output_diff_file"

echo "Diff file generated: $output_diff_file"

exit 0 