.DS_Store
*/.DS_Store
.git-ftp-ignore
.sass-cache
app/webroot/img/generated
app/webroot/img/uploads
app/webroot/bower_components/modernizr/modernizr_build.js
app/tmp/cache/webadmin_menu
app/tmp/cache/data/*
cake
dry_ftp.sh
ftp.sh
node_modules
*.map
.git-ftp-include
*.bak
app/webroot/files/uploads
app/webroot/blog/app/uploads

/vendor/

# Blog app files (WordPress)
# app/webroot/blog/app/plugins/*
!app/webroot/blog/app/plugins/.gitkeep
# app/webroot/blog/app/mu-plugins/*/
app/webroot/blog/app/upgrade
app/webroot/blog/app/uploads
app/webroot/blog/app/uploads/**
!app/webroot/blog/app/uploads/.gitkeep

# WordPress
app/webroot/blog/wp
# app/webroot/blog/.htaccess

# Dotenv
.env
.env.*
!.env.example
app/tmp/*
app/tmp/**/*
app/tmp/cache/*
app/tmp/cache/cake_model_default_db_list
app/tmp/cache/cake_core_object_map
app/tmp/cache/cake_core_file_map
app/tmp/cache/cake_core_dir_map
app/tmp/cache/cake_core_core_paths

*/**/.DS_Store
app/config/.env
app/config/config.php
/dumps
/.tarballs
