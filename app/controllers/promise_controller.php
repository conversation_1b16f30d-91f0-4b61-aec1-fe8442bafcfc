<?php

class PromiseController extends AppController {

  var $name = 'Promise';
  var $uses = []; // Disable the model binding
  var $components = array('Section', 'Navigation');

  public $criticalCss = 'promise';

  function index() {

    $showSidebar = true;
    $section = 'pages';

    $Page = ClassRegistry::init('Page');
    $holidayInfoPage = $Page->getBySlug('holiday_information');
    if ($holidayInfoPage) {
      $navigation = $Page->getNavigation($holidayInfoPage['Page']['id']);

      // Set the correct page as selected in the navigation
      if (!empty($navigation[0]['children'][0])) {
          $navigation[0]['children'][0]['selected'] = true;  // Set selected on the first child
          $navigation[0]['selected'] = false;  // Remove selected from parent
      }
  }

      $SectionController = null;
      $SectionModel = null;
      $sectionData = null;
      $sectionSlug = null;
      $sectionSlugParam = null;
      $section = null;

      $this->set(compact('navigation', 'section', 'sectionController', 'SectionModel', 'sectionData', 'sectionSlug', 'sectionSlugParam'));

    if (empty($this->viewVars['breadcrumbs'])) {
      $breadcrumbs = array(array(
        'text' => "The Bon Voyage Holiday Promise",
        'url'  => $this->here
      ));
    }


    $heroBannerImage = RESOURCES_HOSTNAME . "/img/site/placeholders/spotlights.jpg";

    $sectionHeader = "The Bon Voyage Holiday Promise";

    $hideTheImage = true;

    $this->set(compact('promise', 'breadcrumbs', 'heroBannerImage', 'sectionHeader', 'hideTheImage'));

    $this->_setMeta('The Bon Voyage Holiday Promise');

  }


}

?>
