<?php

class DestinationsController extends AppController {

  var $name = 'Destinations';
  var $components = array('Section', 'Navigation');

  function beforeFilter() {
    parent::beforeFilter();
    if ($this->Auth->allowedActions <> array('*')) {
      $this->Auth->allowedActions = array_merge($this->Auth->allowedActions, array('healthcheck'));
    }
  }


  function webadmin_edit($id = null) {

    parent::webadmin_edit($id);

    $mapData = $this->Destination->getMapData($id, 'editor');

    $this->set(compact('mapData'));

  }

  function view() {

    //This controller uses the section component. The component already
    //returns the destination data and calls a 404 if not found. So in
    //theory it should never reach here if the data is empty, but just
    //in case, here's another check
    if (empty($this->sectionData['Destination'])) {
      $this->cakeError('error404');
    }

    $destination = $this->sectionData;

    $destinationActivities = $this->_findActivities($this->sectionId);

    $related = $this->_findHolidayTypes($this->sectionId);

    $this->set(compact('destination', 'destinationActivities', 'related', 'breadcrumbs'));
  }

  function healthcheck() {
    $this->view();
  }

  /**
   * Returns the children of a destination
   */
  protected function _findChildren($sectionId) {
    $findChildren = function() use ($sectionId) {
      return @$this->Destination->children(
        $sectionId, true, null, null, null, null, 0
      );
    };

    return $this->cacher(implode('_', array(
      'destination', $sectionId,  'children'
    )), $findChildren);
  }

  /**
   * Returns a destinations related activities
   */
  protected function _findActivities($sectionId) {
    $findActivities = function() use ($sectionId) {
      return ClassRegistry::init('Activity')->getActivitiesByDestination($sectionId);
    };

    return $this->cacher(implode('_', array(
      'destination', $sectionId, 'activities'
    )), $findActivities);
  }

  /**
   * Returns a destinations related holiday types
   */
  protected function _findHolidayTypes($sectionId) {
    $findHolidayTypes = function() use ($sectionId) {
      return ClassRegistry::init('HolidayType')->getHolidayTypesOnDestination($sectionId);
    };

    return $this->cacher(implode('_', array(
      'destination', $sectionId, 'holiday_types'
    )), $findHolidayTypes);
  }
}

?>
