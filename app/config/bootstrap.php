<?php
/**
 * Fix for CakePHP srand() warning - define CIPHER_SEED as integer before CakePHP tries to define it as string
 */
if (!defined('CIPHER_SEED')) {
    define('CIPHER_SEED', 768593096);
}

/* SVN FILE: $Id: bootstrap.php 7945 2008-12-19 02:16:01Z gwoo $ */
/**
 * Short description for file.
 *
 * Long description for file
 *
 * PHP versions 4 and 5
 *
 * CakePHP(tm) :  Rapid Development Framework (http://www.cakephp.org)
 * Copyright 2005-2008, Cake Software Foundation, Inc. (http://www.cakefoundation.org)
 *
 * Licensed under The MIT License
 * Redistributions of files must retain the above copyright notice.
 *
 * @filesource
 * @copyright     Copyright 2005-2008, Cake Software Foundation, Inc. (http://www.cakefoundation.org)
 * @link          http://www.cakefoundation.org/projects/info/cakephp CakePHP(tm) Project
 * @package       cake
 * @subpackage    cake.app.config
 * @since         CakePHP(tm) v 0.10.8.2117
 * @version       $Revision: 7945 $
 * @modifiedby    $LastChangedBy: gwoo $
 * @lastmodified  $Date: 2008-12-19 02:16:01 +0000 (Fri, 19 Dec 2008) $
 * @license       http://www.opensource.org/licenses/mit-license.php The MIT License
 */
/**
 *
 * This file is loaded automatically by the app/webroot/index.php file after the core bootstrap.php is loaded
 * This is an application wide file to load any function that is not used within a class define.
 * You can also use this to include or require any files in your application.
 *
 */
/**
 * The settings below can be used to set additional paths to models, views and controllers.
 * This is related to Ticket #470 (https://trac.cakephp.org/ticket/470)
 *
 * $modelPaths = array('full path to models', 'second full path to models', 'etc...');
 * $viewPaths = array('this path to views', 'second full path to views', 'etc...');
 * $controllerPaths = array('this path to controllers', 'second full path to controllers', 'etc...');
 *
 */
//EOF

Configure::load('config');

App::import('Vendor', 'autoload', array('search' => ROOT . DS . 'vendor'));
App::import('Vendor', 'RewriteCacheControlFilter');

if (php_sapi_name() == 'cli') {
  Configure::write('Runtime.invoked', 'commandline');
}

if (isset($_SERVER['HTTP_HOST']) && strstr($_SERVER['HTTP_HOST'], 'local')) {
  Configure::write('Runtime.environment', 'local');
}

if (isset($_SERVER['windir']) || isset($_SERVER['WINDIR'])) {

  Configure::write('Runtime.operating_system', 'windows');

} elseif (
  (Configure::read('Runtime.invoked') == 'webserver' && strstr($_SERVER['HTTP_HOST'], 'dev'))
  || (Configure::read('Runtime.invoked') == 'commandline' && strstr($_SERVER['HOSTNAME'], 'dev-000'))
) {
  Configure::write('Runtime.environment', 'dev');
} elseif (
  (Configure::read('Runtime.invoked') == 'webserver' && strstr($_SERVER['HTTP_HOST'], 'staging'))
  || (Configure::read('Runtime.invoked') == 'commandline' && strstr($_SERVER['HOSTNAME'], 'dev-000'))
) {
  Configure::write('Runtime.environment', 'test');
}

// Configure navigation cache
Cache::config('navigation', array(
    'engine' => 'File',
    'duration' => '+1 minute', // Short duration for testing
    'path' => CACHE . 'navigation' . DS,
    'prefix' => 'nav_'
));
