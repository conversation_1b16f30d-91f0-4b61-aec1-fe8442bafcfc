/* global $$ */

var App = window.App||{};

App.Navigation = (function () {

    'use strict';

    var Navigation = function () {
        this.toggles = $$('[data-toggle]');

        this.toggles.each(function (el) {

            $(el).on('click', this.toggle.bindAsEventListener(this));

        }.bind(this));
    };

    Navigation.prototype.toggle = function (e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('[Search Debug] Toggle event triggered on:', e.target.tagName, e.target.className);

        // Find the toggle element - either the target itself or its parent button
        var toggleElement = e.target;

        // If the target is an image, get its parent button
        if (toggleElement.tagName.toLowerCase() === 'img') {
            console.log('[Search Debug] Click was on image, getting parent button');
            toggleElement = toggleElement.parentNode;
        }

        // If the target doesn't have data-toggle, try to find a parent that does
        if (!toggleElement.hasAttribute('data-toggle')) {
            console.log('[Search Debug] No data-toggle on element, looking for parent');
            var parentButton = toggleElement.up('[data-toggle]');
            if (parentButton) {
                console.log('[Search Debug] Found parent with data-toggle:', parentButton.getAttribute('data-toggle'));
                toggleElement = parentButton;
            }
        }

        // Toggle active class on the toggle element
        $(toggleElement).toggleClassName('active');
        console.log('[Search Debug] Toggle element active state:', $(toggleElement).hasClassName('active'));

        // Get the target from the data-toggle attribute
        var target = $(toggleElement).readAttribute('data-toggle');
        console.log('[Search Debug] Target element:', target);

        // Toggle active class on the target element
        if (target) {
            $(target).toggleClassName('active');
            console.log('[Search Debug] Target element active state:', $(target).hasClassName('active'));
            
            // If this is a search toggle, ensure the search input is focused
            if (target === 'search') {
                var searchInput = $('search').down('input[type="text"]');
                if (searchInput) {
                    console.log('[Search Debug] Focusing search input');
                    searchInput.focus();
                } else {
                    console.log('[Search Debug] Could not find search input element');
                }
            }
        }
    };

    return Navigation;

}());
