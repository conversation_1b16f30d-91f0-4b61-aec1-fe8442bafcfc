/* global $$, $A, Class, Effect, Event, Prototype, H5F, SimpleSlider, Handlebars, FastClick */

var App = window.App||{};
var UI = window.UI||{};

if(!Array.indexOf){
  Array.prototype.indexOf = function(obj){
    for(var i=0; i<this.length; i++){
      if(this[i] === obj){
        return i;
      }
    }
    return -1;
  };
}

(function(w){
  var id = 0;
  var head = $$('head')[0];

  w.getJSON = function(url, callback) {
    var script = document.createElement('script'), token = '__jsonp' + id;
    w[token] = callback;
    script.src = url.replace(/\?(&|$)/, '__jsonp' + id + '$1');
    w.onload = function() {
      script.remove();
      script = null;
      delete w[token];
    };
    head.appendChild(script);
    id++;
  };
})(window);

(function(){
  function wheel(event){
    var delta;
    // normalize the delta
    if (event.wheelDelta) { // IE & Opera
      delta = event.wheelDelta / 120;
    } else if (event.detail) { // W3C
      delta =- event.detail / 3;
    }

    if (!delta) {
      return;
    }

    var customEvent = event.element().fire('mouse:wheel',{
      delta: delta
    });

    if (customEvent.stopped) {
      event.stop();

      return false;
    }
  }

  document.observe('mousewheel', wheel);
  document.observe('DOMMouseScroll', wheel);
})();

Element.addMethods({
  makeInvisible : function(element){
    return $(element).setStyle({
      'visibility' : 'hidden'
    });
  },
  makeVisible : function(element){
    return $(element).setStyle({
      'visibility' : ''
    });
  }
});

UI.Modal = Class.create({
  initialize : function(options){
    this.options = Object.extend({
      className : 'ui-modal',
      minHeight: 100,
      minWidth : 200,
      offset : 50,
      rootElement : $$('body').first()
    }, options||{});

    this.build();
  },
  build : function(){
    this.modal = new Element('div', {className: this.options.className}).setStyle({
      'overflow' : 'visible',
      'position' : 'absolute',
      'zIndex' : 500
    }).hide();
    this.content = new Element('div', {className: 'ui-modal-content'});
    this.modal.insert(this.content);
    if (!this.options.hideCloseButton) {
      this.close = new Element('a', {href: '#', className: 'ui-modal-close'}).update('Close');
      this.modal.insert(this.close);
      this.close.observe('click', this.hide.bindAsEventListener(this));
    }
    this.options.rootElement.insert(this.modal);
  },
  position : function(){
    //this.resize(this.modal.getDimensions());
  },
  getDimensions : function(dimensions){

    var viewport = document.viewport.getDimensions();
    var scroll = document.viewport.getScrollOffsets();
    var style = this.modal.style;

    var width = Math.min((viewport.width-(this.options.offset*2)), Math.max(dimensions.width, this.options.minWidth));
    style.width = width+'px';
    var height = Math.min((viewport.height-(this.options.offset*2)), Math.max(dimensions.height, this.options.minHeight));

    style.height = height+'px';
    // style.height = 'auto';

    var top = Math.max((this.options.offset+scroll.top), (scroll.top+(viewport.height/2)-(height/2)));
    style.top = top+'px';
    var left = Math.max((this.options.offset+scroll.left), (scroll.left+(viewport.width/2)-(width/2)));
    style.left = left+'px';

    var d = {
      height: 'auto',
      left: left+'px',
      top: top+'px',
      width: width+'px'
    };

    if(!this.d){
      var h = this.options.minHeight;
      var w = this.options.minWidth;
      var t = Math.max((this.options.offset+scroll.top), (scroll.top+(viewport.height/2)-(h/2)));
      var l = Math.max((this.options.offset+scroll.left), (scroll.left+(viewport.width/2)-(w/2)));
      style.width = w+'px';
      style.height = h+'px';
      style.top = t+'px';
      style.left = l+'px';
    }else{
      this.modal.setStyle(this.d);
      this._d = this.d;
    }

    this.d = d;
    return d;
  },
  show : function(){
    this.modal.makeInvisible().show();
    this.position();
    this.modal.makeVisible();
  },
  hide : function(){

  }
});

UI.Lightbox = Class.create(UI.Modal, {
  initialize : function($super, selector, options){
    $super(options);

    this.overlay = new Element('div', {className: 'ui-overlay'}).setStyle({
      background: '#000000',
      opacity: 0.8,
      display: 'none'
    });
    this.modal.insert({before:this.overlay});

    this.wrapper = new Element('div', {className:'lightbox'}).update('<div class="t"><div class="tl"></div><div class="tr"></div></div><div class="l"><div class="r"></div></div><div class="f"><div class="fl"></div><div class="fr"></div></div>');
    this.content.insert(this.wrapper);
    this.el = this.wrapper.down('div.r');
    this.top = this.wrapper.down('div.t');

    this.selector = selector;
    document.observe('click', this.onclick.bindAsEventListener(this));
    this.setup();
  },
  setup : function(){
    this._slideshow = Class.create(UI.Slideshow, {
      initialize : function($super, element, slides, options){
        $super(element, slides, options);
      },
      onLoad : function(image){
        var slideshow = this.slideshow;

        slideshow.element.makeInvisible();
        slideshow.image1.src = image.src;
        slideshow.div2.hide();
        slideshow.status.update(slideshow.options.statusText.interpolate({
          index: slideshow.index+1,
          count: slideshow.slides.size()
        }));

        var d = this.getDimensions({
          height: image.height+50,
          width: image.width+20
        });


        if(d){

          var _d = {
            width : image.width+'px',
            height : image.height+31+'px'
          };

          slideshow.div1.hide();
          slideshow.element.makeVisible();

          if(slideshow.image2.src){
            slideshow.div2.show();
            new Effect.Fade(slideshow.div2, {
              duration: 0.25,
              queue: 'end'
            });
          }


          if(Object.toQueryString(d) !== Object.toQueryString(this._d)){
            new Effect.Parallel([
              new Effect.Morph(this.modal, {
                style : d,
                sync: true
              }),
              new Effect.Morph(slideshow.element, {
                style : _d,
                sync: true
              })
            ], {
              duration: 0.25,
              queue: 'end'
            });
          }

          new Effect.Appear(slideshow.div1, {
            queue: 'end',
            duration: 0.25,
            afterFinish : function(){
              this.element.setStyle({
                height: _d.height,
                width : _d.width
              });
              this.image2.src = this.image1.src;
              if(this.playing){
                this._play();
              }
              this.controls.makeVisible();
            }.bind(slideshow)
          });

          var url = encodeURIComponent(window.location.href);
          var media = encodeURIComponent(image.src);

          this.top.down('div.tl').update('<a href="http://www.pinterest.com/pin/create/button/?url=' + url + '&media=' + media + '"data-pin-do="buttonPin"data-pin-config="none"><img src="//assets.pinterest.com/images/pidgets/pin_it_button.png" /></a>');

          this.top.down('div.tl').addClassName('pin-it-btn');

          var f = document.createElement('script');
          f.async = true;
          f.id = 'pinterest-script';
          f.src = '//assets.pinterest.com/js/pinit.js';
          document.getElementById('async-scripts').appendChild(f);

        }
      }.bind(this)
    });
  },
  onclick : function(e){

    var element = e.findElement(this.selector), index = 0, slides = $A();

    if(element){
      e.stop();

      var h = document.body.clientHeight;

      this.overlay.setStyle({
        top: 0,
        height: h+'px',
        left: 0,
        position: 'absolute',
        width: '100%',
        zIndex: 400,
        opacity: 0
      }).show();

      new Effect.Fade(this.overlay, {
        duration: 0.25,
        from: 0,
        to: 0.8,
        queue: 'end'
      });

      if(element.rel){
        var rel = element.rel;
        $$(this.selector+'[rel='+rel+']').each(function(slide, i){
          slides.push([slide.href, slide.title]);
          if(slide===element){
            index = i;
          }
        });
        this.show();
        this.slideshow = new this._slideshow(this.el, slides, {
          index: index
        });
        this.slideshow.element.setStyle({
          height: '124px',
          width: '170px'
        });
        this.slideshow.show();
      }else{
        this.load(element.href, element.title);
      }
    }
  },
  load : function(src, title){
    var image = new Image();
    image.onload = this.onLoad.bind(this, image);
    image.src = src;
    image.alt = title;

    var url = encodeURIComponent(window.location.href);

    this.top.down('div.tl').update('<a href="http://www.pinterest.com/pin/create/button/?url=' + url + '&media=' + src + '"data-pin-do="buttonPin"data-pin-config="none"><img src="//assets.pinterest.com/images/pidgets/pin_it_button.png" /></a>');
    this.top.down('div.tl').addClassName('pin-it-btn');

    var f = document.createElement('script');
    f.async = true;
    f.id = 'pinterest-script';
    f.src = '//assets.pinterest.com/js/pinit.js';
    document.getElementById('async-scripts').appendChild(f);
  },
  onLoad : function(image){

    this.el.update(image);

    var d = this.getDimensions({
      height: (image.naturalHeight || image.height) + 50,
      width: (image.naturalWidth || image.width) + 20
    });

    this.modal.setStyle(d);

    new Effect.Appear(this.modal, {
      duration: 0.25,
      queue : 'end'
    });
  },
  hide : function(e){
    if(e){
      e.stop();
    }
    new Effect.Fade(this.modal, {
      duration: 0.25,
      afterFinish : function(){
        if(this.slideshow){
          this.slideshow.destroy();
        }
        this.d = this._d = {};
      }.bind(this),
      queue : 'end'
    });
    new Effect.Fade(this.overlay, {
      duration: 0.25,
      from: 0.8,
      to: 0,
      queue: 'end'
    });
  }
});

UI.Slideshow = Class.create({
  initialize : function(element, slides, options){
    this.slides = slides;
    this.root = $(element);

    this.options = Object.extend({
      className : 'ui-slideshow',
      controlsClassName : 'ui-slideshow-controls',
      delay : 5,
      index: 0,
      onLoad : Prototype.emptyFunction,
      onPause : Prototype.emptyFunction,
      onPlay : Prototype.emptyFunction,
      nextClassName : 'ui-slideshow-next',
      nextText : 'Next',
      pauseClassName : 'ui-slideshow-pause',
      pauseText : 'Pause',
      playClassName : 'ui-slideshow-play',
      playText : 'Play',
      previousClassName : 'ui-slideshow-previous',
      previousText : 'Previous',
      statusClassName : 'ui-slideshow-status',
      statusText : 'Image #{index} of #{count}'
    }, options||{});

    this.index = this.options.index;

    this.build();
    //this.show();
  },
  build : function(){
    this.element = new Element('div', {className: this.options.className}).update('<div><div><img /></div><div><img /></div></div><div class="ui-slideshow-controls"><ul class="ui-slideshow-player-controls"><li class="#{playClassName}"><a href="#">#{playText}</a></li><li class="#{pauseClassName}"><a href="#">#{pauseText}</a></li></ul><ul class="ui-slideshow-manual-controls"><li class="#{previousClassName}"><a href="#">#{previousText}</a></li><li class="#{nextClassName}"><a href="#">#{nextText}</a></li></ul><p class="#{statusClassName}"></p></div>'.interpolate(this.options));
    this.element.identify();
    this.wrapper = this.element.down('div').setStyle({position: 'relative', width: '100%'});

    this.div1 = this.wrapper.down('div');
    this.div2 = this.div1.next().setStyle({'position' : 'absolute', 'top' : 0, 'left' : 0});

    this.image1 = this.div1.down('img').setStyle({'display' : 'block'});
    this.image2 = this.div2.down('img').setStyle({'display': 'block'});

    this.status = this.element.down('.'+this.options.statusClassName);
    this.previousLink = this.element.down('.'+this.options.previousClassName);
    this.nextLink = this.element.down('.'+this.options.nextClassName);
    this.playLink = this.element.down('.'+this.options.playClassName);
    this.pauseLink = this.element.down('.'+this.options.pauseClassName).hide();
    this.controls = this.element.down('.'+this.options.controlsClassName);
    this.root.insert(this.element);
    this.element.observe('click', this.onClick.bindAsEventListener(this));

    if(this.options.height && this.options.width){
      this.wrapper.setStyle({
        height: this.options.height+'px',
        width: this.options.width+'px',
        overflow : 'hidden'
      });
    }

  },
  show : function(i){
    if (i) {
      this.index = i;
    }

    this.controls.makeInvisible();
    this.nextLink[(this.index+1===this.slides.size()) ? 'hide' : 'show']();
    this.previousLink[(this.index===0) ? 'hide' : 'show']();

    var image = new Image();
    image.onload = this.onLoad.bind(this, image);
    image.src = this.slides[this.index][0];
  },
  onLoad : function(image){

    // var animations = [
    //   new Effect.Appear(this.div1, {
    //     sync : true
    //   })
    // ];

    var animations = [];

    if(this.image2.src){
      this.div2.show();
      animations.push(new Effect.Fade(this.div2, {
        sync : true
      }));
    }

    this.image1.src = image.src;
    this.status.update(this.options.statusText.interpolate({
      index: this.index+1,
      count: this.slides.size()
    }));


    new Effect.Parallel(animations, {
      afterFinish : function(){
        this.image2.src = this.image1.src;
        if(this.playing){
          this._play();
        }
        this.controls.makeVisible();
      }.bind(this)
    });

  },
  onClick : function(e){
    if(e.findElement('.'+this.options.nextClassName)){
      e.stop();
      this._pause();
      this.navigate(1);
    }else if(e.findElement('.'+this.options.previousClassName)){
      e.stop();
      this._pause();
      this.navigate(-1);
    }else if(e.findElement('.'+this.options.playClassName)){
      e.stop();
      this._pause();
      this.play();
    }else if(e.findElement('.'+this.options.pauseClassName)){
      e.stop();
      this.pause();
    }
  },
  navigate : function(i){
    this.index += i||0;

    if (this.index === this.slides.size()) {
      this.index = 0;
    }

    if (this.index === -1) {
      this.index = this.slides.size()-1;
    }

    this.show();
  },
  play : function(){
    this._play();
    this.playLink.hide();
    this.pauseLink.show();
    this.options.onPlay();
  },
  _play : function(){
    this.playing = true;
    this.player = this.navigate.bind(this, 1).delay(this.options.delay);
  },
  pause : function(){
    this._pause();
    this.playLink.show();
    this.pauseLink.hide();
    this.options.onPause();
  },
  _pause : function(){
    window.clearTimeout(this.player);
    this.player = this.playing = false;
  },
  destroy : function(){
    if(this.playing){
      this._pause();
    }
    this.element.stopObserving();
    this.element.remove();
  }
});

App.firstWordBold = function () {
  'use strict';

  var $elements = $$('.js-first-word-bold');

  $elements.each(function ($el) {
    $el.update($el.innerText.replace(/^(\w+) /, '<b>$1</b> '));
  });
};

App.MobileNav = (function() {
    'use strict';

    var MobileNav = function ($el) {

        this.$el = $el;

        this.$select = this.$el.down('select');

        var url = this.$select.getValue();

        if (url !== '#') {
          console.log('Pre-selected', url);
          window.location = url;
        }

        this.$select.on('change', function () {

            url = $(this).getValue();

            console.log('Change:', url);

            if (url !== '#') {
                window.location = url;
            }

        });

    };

    return MobileNav;

})();

App.HeroCarousel = (function() {
    'use strict';

    var HeroCarousel = function ($el) {

        if (typeof SimpleSlider !== 'function') {
            return;
        }

        this.$el = $el;

        this.addImages();

        this.initSlider();

        this.initNavigation();

        return this.$el;

    };

    HeroCarousel.prototype.initSlider = function () {

        this.$slider = new SimpleSlider(this.$el,
            {
                transitionProperty: 'opacity',
                startValue: 0,
                visibleValue: 1,
                endValue: 0,
                transitionDelay: 5,
                transitionDuration: 1
            }
        );

    };

    HeroCarousel.prototype.initNavigation = function () {

        $$('.hero--carousel__nav--left').first().on('click', function (e) {

            e.preventDefault();

            this.$slider.prev();

        }.bind(this));

        $$('.hero--carousel__nav--right').first().on('click', function (e) {

            e.preventDefault();

            this.$slider.next();

        }.bind(this));

    };

    HeroCarousel.prototype.addImages = function () {

        if (typeof window.sliderImages !== 'string') {
            return;
        }

        var sliderImages = JSON.parse(window.sliderImages);

        sliderImages.each(function (image) {

            image.imageClass = image.image.slice(0, image.image.indexOf('.'));

            var html = Handlebars.templates.heroImageTemplate(image);

            this.$el.insert(html);

        }, this);

        window.respimage();

    };

    return HeroCarousel;

}());

function ready(){

  var form = $$('.contact-form, .js-travel-plan-form').first();

  function handleSubmit(event) {
    event.preventDefault()

    grecaptcha.ready(function() {
      grecaptcha.execute('6LenjsgZAAAAAH-f4aj1WPD8Rflsj57FOaAcvtkU', { action: 'contact_form' }).then(function(token) {
        let model = 'QuoteRequest';

        let travelPlanUrls = ['travel_plans', 'start_planning_now', 'make_an_enquiry','social'];
        let isTravelPlanUrl = false;
        travelPlanUrls.each(url => {
          if (location.pathname.includes(url)) {
            isTravelPlanUrl = true;
          }
        });

        if (isTravelPlanUrl) {
          model = 'TravelPlan';
        }

        if (location.pathname.includes('contact')) {
          model = 'Contact';
        }

        if (location.pathname.includes('subscriptions')) {
          model = 'Subscription';
        }

        form.insert('<input type="hidden" name="data[' + model + '][token]" value="' + token + '">');

        formHandler.stop();
        form.submit();
      });
    });
  }

  var formHandler = form && form.on('submit', handleSubmit)

  var $slider = $$('.hero__images').first();

  if (window.sliderImages && sliderImages.length > 1) {
      new App.HeroCarousel($slider);
  }

  new UI.Lightbox('a.lightbox');

  H5F.setup($$('.enews-module__form'));

  var sidebar = $$('.js-page-left-sidebar').first();

  App.firstWordBold();

  new App.Navigation();

  if (sidebar && sidebar.getStyle('height') !== null) {
      $$('.js-page-content-body').first().setStyle({
          minHeight: sidebar.getHeight() + 'px'
      });
  }

  $$('.js-section-content').each(function (el) {
      new App.sectionContent(el);
  });

  $$('.js-mobile-nav').each(function (nav) {
      new App.MobileNav(nav);
  });

  $$('.js-tile-panel').each(function (panel) {
      new App.AccommPanel(panel);
  });

  $$('.js-feefo-widget').each(function (panel) {
      new App.FeefoWidget(panel);
  });

  $$('.js-itinerary-day').each(function (panel) {
      new App.ItineraryDay(panel);
  });

  $$('.js-show-map').each(function (el) {
    el.on('click', function () {
      $$('.image-and-map__map-wrapper').first().addClassName('active');
    });
  });

  $$('.image-and-map__hide').each(function (el) {
    el.on('click', function () {
      $$('.image-and-map__map-wrapper').first().removeClassName('active');
    });
  });

  // FastClick.attach(document.body);
}

if (document.readyState !== 'loading') {
  setTimeout(ready, 1);
} else {
  document.observe('dom:loaded', ready);
}
