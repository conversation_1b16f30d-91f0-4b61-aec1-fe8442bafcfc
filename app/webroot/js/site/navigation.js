/* global $$ */

var App = window.App||{};

App.Navigation = (function () {

    'use strict';

    var Navigation = function () {
        this.toggles = $$('[data-toggle]');

        this.toggles.each(function (el) {

            $(el).on('click', this.toggle.bindAsEventListener(this));

        }.bind(this));
    };

    Navigation.prototype.toggle = function (e) {
        e.preventDefault();

        // Find the toggle element - either the target itself or its parent button
        var toggleElement = e.target;

        // If the target doesn't have data-toggle, try to find a parent that does
        if (!toggleElement.hasAttribute('data-toggle')) {
            // Check if the target is inside a button with data-toggle
            var parentButton = toggleElement.up('[data-toggle]');
            if (parentButton) {
                toggleElement = parentButton;
            }
        }

        // Toggle active class on the toggle element
        $(toggleElement).toggleClassName('active');

        // Get the target from the data-toggle attribute
        var target = $(toggleElement).readAttribute('data-toggle');

        // Toggle active class on the target element
        if (target) {
            $(target).toggleClassName('active');
        }
    };

    return Navigation;

}());

//# sourceMappingURL=navigation.js.map
