/**
 * Universal search toggle fix for both main site and blog
 * This script fixes the issue with the search toggle button when it contains an image
 * It works by removing the data-toggle attribute to prevent the original handler from running
 */
(function() {
    // Set a flag to indicate this script has run
    window.searchToggleFixedUniversal = true;

    console.log('[Search Toggle Fix] Initializing universal search toggle fix');

    // Function to initialize the search toggle fix
    function initSearchToggleFix() {
        // Find all search toggle buttons (there might be multiple on the page)
        const searchToggles = document.querySelectorAll('.search-toggle');

        if (searchToggles.length > 0) {
            console.log('[Search Toggle Fix] Found ' + searchToggles.length + ' search toggle button(s)');

            // Fix each search toggle button
            searchToggles.forEach(function(searchToggle) {
                // Skip if already fixed
                if (searchToggle.hasAttribute('data-search-toggle-fixed')) {
                    return;
                }

                // Store the original data-toggle value if it exists
                const originalToggleTarget = searchToggle.getAttribute('data-toggle');

                // Remove any existing click event listeners by cloning the element
                const newSearchToggle = searchToggle.cloneNode(true);
                searchToggle.parentNode.replaceChild(newSearchToggle, searchToggle);

                // CRITICAL: Remove the data-toggle attribute to prevent App.Navigation from handling it
                if (newSearchToggle.hasAttribute('data-toggle')) {
                    console.log('[Search Toggle Fix] Removing data-toggle attribute');
                    newSearchToggle.removeAttribute('data-toggle');
                }

                // Add our click event listener
                newSearchToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation(); // Prevent event bubbling

                    // Toggle 'active' class on the button
                    this.classList.toggle('active');

                    // Toggle hamburger/cross icons if they exist
                    const hamburgerIcon = this.querySelector('.hamburger-icon');
                    const crossIcon = this.querySelector('.cross-icon');

                    if (hamburgerIcon && crossIcon) {
                        if (this.classList.contains('active')) {
                            hamburgerIcon.style.display = 'none';
                            crossIcon.style.display = 'block';
                        } else {
                            hamburgerIcon.style.display = 'block';
                            crossIcon.style.display = 'none';
                        }
                    }

                    // Try multiple ways to find the search element
                    let searchElement = null;

                    // Method 1: Use the original data-toggle value
                    if (originalToggleTarget) {
                        searchElement = document.getElementById(originalToggleTarget) ||
                                       document.querySelector('.' + originalToggleTarget);
                    }

                    // Method 2: Try to find by ID 'search'
                    if (!searchElement) {
                        searchElement = document.getElementById('search');
                    }

                    // Method 3: Try to find by class
                    if (!searchElement) {
                        searchElement = document.querySelector('.primary-search');
                    }

                    if (searchElement) {
                        // Toggle 'active' class on the search element
                        searchElement.classList.toggle('active');
                        console.log('[Search Toggle Fix] Toggled search element visibility');

                        // For mobile, ensure proper display style
                        if (window.innerWidth < 1024) {
                            if (searchElement.classList.contains('active')) {
                                searchElement.style.display = 'block';
                            } else {
                                // Only hide on mobile
                                searchElement.style.display = '';
                            }
                        }
                    } else {
                        console.error('[Search Toggle Fix] Search element not found');
                    }
                });

                // Mark the button as fixed
                newSearchToggle.setAttribute('data-search-toggle-fixed', 'true');
                console.log('[Search Toggle Fix] Search toggle button fixed');
            });
        } else {
            console.warn('[Search Toggle Fix] No search toggle buttons found');
        }
    }

    // Function to check if we need to run the fix
    function checkAndFix() {
        // Check if there are any unfixed search toggle buttons
        const unfixedButtons = document.querySelectorAll('.search-toggle:not([data-search-toggle-fixed])');
        if (unfixedButtons.length > 0) {
            console.log('[Search Toggle Fix] Found ' + unfixedButtons.length + ' unfixed buttons, applying fix');
            initSearchToggleFix();
        }
    }

    // Try to run as early as possible
    initSearchToggleFix();

    // Also run on DOMContentLoaded as a fallback
    document.addEventListener('DOMContentLoaded', function() {
        checkAndFix();
    });

    // Also run when the window loads, as a final fallback
    window.addEventListener('load', function() {
        checkAndFix();
    });

    // Run periodically for the first few seconds after page load
    // This helps catch any dynamically added buttons
    let checkCount = 0;
    const maxChecks = 5;
    const checkInterval = setInterval(function() {
        checkCount++;
        checkAndFix();
        if (checkCount >= maxChecks) {
            clearInterval(checkInterval);
        }
    }, 1000);
})();
