/* Base styles */
.page-header {
  position: relative;
  z-index: 1000;
}

/* Secondary Navigation */
.secondary-nav {
  position: relative;
  display: flex;
}

.secondary-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.secondary-nav li {
  margin: 0;
  position: relative;
  padding: 0 10px;
}

.secondary-nav__dest {
  color: #fff;
  display: block;
  padding: 10px 5px;
  text-decoration: none;
  white-space: nowrap;
}

.secondary-nav li:hover .secondary-nav__dest,
.secondary-nav li:has(.mega-menu__panel:hover) .secondary-nav__dest,
.secondary-nav li:has(.mega-menu__panel:visible) .secondary-nav__dest,
.secondary-nav li:has(.mega-menu__panel[style*="display: block"]) .secondary-nav__dest {
  color: #9a1e13;
  text-decoration: none;
}

.secondary-nav li:hover,
.secondary-nav li:has(.mega-menu__panel:hover),
.secondary-nav li:has(.mega-menu__panel:visible),
.secondary-nav li:has(.mega-menu__panel[style*="display: block"]) {
  background-color: #fff;
}

.secondary-nav__dest svg {
  margin-left: 7px;
  vertical-align: middle;
  position: relative;
  top: -1px;
}

/* Override the pseudo-element */
.secondary-nav a:after {
  display: none;
}

/* Desktop Navigation */
.desktop-nav {
  display: none;
}

@media (min-width: 1024px) {


  /* Show desktop nav */
  .desktop-nav {
    display: block;
    position: relative;
    margin-top: 20px;
  }

  .main-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    justify-content: flex-end;
  }

  .main-nav > li {
    position: relative;
    padding: 15px 25px;
  }

  .main-nav > li > a {
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-size: 14px;
    white-space: nowrap;
  }

  /* Dropdown styles */
  .main-nav > li > ul {
    background: #fff;
    position: absolute;
    left: 0;
    top: 100%;
    min-width: 200px;
    list-style: none;
    padding: 0;
    margin: 0;
    display: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 1000;
  }

  .main-nav > li > ul > li {
    padding: 0;
  }

  .main-nav > li > ul > li > a {
    color: #565044;
    padding: 10px 20px;
    display: block;
    text-decoration: none;
    font-size: 14px;
  }

  .main-nav > li > ul > li > a:hover {
    background: #f5f5f5;
    color: #a80000;
  }

  /* Show dropdown on hover */
  .main-nav > li:hover > ul {
    display: block;
    animation: slideDown 0.3s ease-in-out;
  }

  /* Hover effect for main items */
  .main-nav > li:hover > a {
    color: #f5f5f5;
  }

  /* Dropdown arrow */
  .dropdown-arrow {
    font-size: 10px;
    margin-left: 5px;
    vertical-align: middle;
  }

  .secondary-nav > ul > li {
    padding-bottom: 10px;
  }
}

/* Animation */
@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (min-width: 1024px) {
  .mobile-search {
    display: none;
  }

  .desktop-search {
    position: absolute;
    top: 12px;
    right: 18px;
  }
}

/* mmenu specific styles */
.mm-menu {
    --mm-color-background: #a80000;
    --mm-color-text: #fff;
    --mm-color-button: #fff;
    --mm-color-border: rgba(255,255,255,0.3);
}

.mm-listitem {
    color: #fff;
}

/* .mm-listitem__text {
    color: #fff !important;
} */

/* Ensure menu is visible */
#menu {
    display: block !important;
}

/* Desktop Mega Menu */
@media (min-width: 1024px) {
  .desktop-nav {
    position: relative;
    background: #a80000;
  }

  .main-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .main-nav > li {
    position: static; /* Changed from relative for full-width mega menu */
  }

  .main-nav > li > a {
    display: block;
    padding: 16px 25px;
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    font-size: 14px;
    transition: background 0.3s;
  }

  /* Mega Menu Container */
  .mega-menu {
    position: absolute;
    left: 0;
    right: 0;
    top: 85px;
    z-index: 998;
  }

  /* Individual Mega Menu Panels */
  .mega-menu__panel {
    display: none;
    position: absolute;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 7px 10px rgba(0,0,0,0.1);
    padding: 30px;
    animation: fadeIn 0.2s ease-in-out;
    margin-top: 0;
    border-top: none !important;
    gap: 2px;
  }

  .mega-menu__panel.is-active {
    display: block;
  }

  /* Panel Inner Layout */
  .mega-menu__inner {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    gap: 40px;
  }

  .mega-menu__section {
    flex: 1;
  }
}

/* Hide on Mobile */
@media (max-width: 1023px) {
  .mega-menu {
    display: none;
  }
  .search-toggle img {
    width: 25px;
    height: 25px;
    display: block;
  }
}

/* Remove any display:none or visibility:hidden that might be affecting the menu */
#menu,
.desktop-nav,
.main-nav {
    display: block;
    visibility: visible;
}

/* Desktop header/menu (1024px+) */
@media (min-width: 1024px) {
    #menu {
        display: none;  /* Only hide on desktop */
    }
    .desktop-nav {
        display: block;
    }
}

/* Mobile header/menu (below 1024px) */
@media (max-width: 1023px) {
    .desktop-nav {
        display: none;  /* Only hide on mobile */
    }
    #menu {
        display: block;
    }

    .page-header__inner {
      flex-direction: column;
        padding: 0;
    }
    .desktop-menu {
    display: none!important;
    }
}

/* Hide mega menu panels by default */
.mega-menu__panel {
    display: none !important;
    position: absolute;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    margin-top: 5px;
}

/* Show panel when parent is hovered */
.secondary-nav__dest:hover + .mega-menu__panel,
.mega-menu__panel:hover {
    display: block !important;
}

/* Layout for mega menu sections */
.mega-menu__section {
    float: left;
    /* width: 70%; */
    padding: 20px;
}



/* Mobile-first approach */
.desktop-menu {
    display: none;
    margin-left: 15px;
    margin-right: 40px;
    width: max-content;

}

/* Mobile nav hiding */
@media (max-width: 1023px) {

    /* Hide regular nav items on mobile */
    .primary-nav,
    .secondary-nav {
        display: none!important;
    }
}

/* Desktop nav showing */
@media (min-width: 1024px) {
    .desktop-menu {
        display: block;
        margin-top: 15px;
    }

    /* Hide mobile menu on desktop */
    #mobile-menu {
        display: none;
    }
                .page-header__inner {
                padding: 0 15px;
            }
}

/* Mega Menu Styling */
.mega-menu {
    position: absolute;
    left: 0;
    right: 0;
    top: 85px;
    z-index: 998;
}

.mega-menu__panel {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 7px 10px rgba(0,0,0,0.1);
    padding: 30px;
    animation: fadeIn 0.2s ease-in-out;
    border-top: none !important;
    gap: 2px;
}

.mega-menu__panel.is-active {
    display: block;
}

.mega-menu__inner {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    gap: 40px;
}

.mega-menu__section {
    flex: 1;
}

.mega-menu__section h3 {
    color: #565044;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 12px;
    text-transform: uppercase;
}

.mega-menu__section ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.mega-menu__section li {
    margin-bottom: 0;
}

/* Basic mmenu styling */
.mm-menu {
    --mm-color-background: #333;
    --mm-color-text: #fff;
    --mm-color-button: #fff;
    --mm-color-border: rgba(255, 255, 255, 0.1);
    background: var(--mm-color-background);
    transition: transform 0.3s ease !important;
    /* width: 80% !important; */
    /* max-width: 312px !important; */
}

/* Ensure panels don't affect menu width */
.mm-panels, .mm-panel {
    /* width: 100% !important; */
}

/* .mm-panel:not(.mm-panel--opened):not([data-mm-parent]) {
    transform: translate3d(-100%, 0, 0) !important;
}

.mm-panel:not(.mm-panel--opened)[data-mm-parent] {
    transform: translate3d(100%, 0, 0) !important;
}

.mm-panel--opened {
    transform: translate3d(0, 0, 0) !important;
} */

/* Ensure menu is hidden by default on mobile */
#mobile-menu {
    /* Let mmenu handle visibility */
}

/* When menu is opened */
.mm-wrapper_opening #mobile-menu,
#mobile-menu.mm-menu_opened {
    visibility: visible;
}

/* Hide mobile menu on desktop */
@media (min-width: 1024px) {
    #mobile-menu {
        display: none !important;
    }
}

/* Hamburger styling */
.mmenu-trigger {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 12px;
    left: 8px;
    position: absolute;
}

.mmenu-trigger img {
    width: 30px;
    height: 30px;
    transition: all 0.3s ease;
}

.hamburger-icon,
.cross-icon {
    display: block;
}

/* Hide hamburger on desktop */
@media (min-width: 1024px) {
    .mmenu-trigger {
        display: none;
    }
}

/* Mmenu content styling */
.mm-menu {
    --mm-color-background: #333;
    --mm-color-text: #fff;
    --mm-color-border: rgba(255, 255, 255, 0.1);
}

.mm-menu a {
    color: var(--mm-color-text);
}

.mm-listitem {
    color: var(--mm-color-text);
    border-color: var(--mm-color-border);
}

.mm-navbar {
    border-color: var(--mm-color-border);
}

.mm-navbar__title {
    color: var(--mm-color-text);
}

/* Make sure mmenu is visible when open */
.mm-wrapper_opening .mm-menu_offcanvas {
    display: block !important;
}

.mm-menu_offcanvas {
    display: block !important;
}

/* Basic mmenu styling */
.mm-menu {
    --mm-color-background: #333;
    --mm-color-text: #fff;
    --mm-color-border: rgba(255, 255, 255, 0.1);
    background: var(--mm-color-background);
}

.mm-listitem {
    color: var(--mm-color-text);
    border-color: var(--mm-color-border);
}

.mm-navbar {
    border-color: var(--mm-color-border);
}

/* Mega Menu Structure */
.nav-wrapper {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.mega-menu {
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  z-index: 998;
}

.mega-menu__panel {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  box-shadow: 0 7px 10px rgba(0,0,0,0.1);
  padding: 30px;
  animation: fadeIn 0.2s ease-in-out;
  border-top: none !important;
  gap: 2px;
  z-index: 1000;
}

.mega-menu__list--columns {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  grid-auto-flow: column;
  grid-template-rows: repeat(7, auto);
  gap: 2px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

/* Special handling for holiday info dropdown */
#holiday-info-dropdown .mega-menu__list--columns {
    grid-auto-columns: minmax(auto, 350px);
    grid-template-columns: none;
    grid-auto-flow: column;
}

/* Panel is shown when parent link is hovered or focused */
.secondary-nav__dest:hover + .mega-menu__panel,
.secondary-nav__dest:focus + .mega-menu__panel,
.mega-menu__panel:hover {
  display: block;
}

.mega-menu__inner {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.mega-menu__section {
  min-width: 300px;
}

.mega-menu__section h3 {
  color: #565044;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 15px 12px;
  text-transform: uppercase;
}

.mega-menu__section ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mega-menu__section li {
  margin-bottom: 0;
}

.mega-menu__section a {
  color: #565044;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.mega-menu__section a:hover {
  color: #a80000;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .mega-menu {
    display: none; /* Hide on mobile/tablet */
  }
}

/* Active state for dropdown triggers */
.secondary-nav__dest[aria-expanded="true"] {
  color: #a80000;
}

/* Optional: Add a subtle indicator for dropdown items */
.secondary-nav__dest {
  position: relative;
  /* padding-right: 20px; */
}

.secondary-nav__dest::after {
  content: '';
  /* position: absolute; */
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  border: 4px solid transparent;
  border-top-color: currentColor;
  margin-top: 2px;
}

.mega-menu {
    position: absolute;
    left: 0;
    right: 0;
    top: 85px;
    z-index: 998;
}

.mega-menu__panel {
    display: none; /* Hidden by default */
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    margin-top: 5px;
}

/* Debug styles - remove later */
.mega-menu__panel[style*="display: block"] {
    display: block !important;
    background: #fff;
    box-shadow: 0 12px 10px rgba(0, 0, 0, 0.1);
}

/* Ensure main nav stays on top */
.secondary-nav {
    position: static;
}

/* Search field styling */
@media (min-width: 1024px) {
  .primary-search {
      flex-shrink: 1;
  }
}


/* Mobile search */
.mobile-search {
    padding: 15px;
}

.mobile-search input[type="text"],
.desktop-search input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: #fff;
    border-radius: 4px;
}

.mobile-search input[type="text"]::placeholder,
.desktop-search input[type="text"]::placeholder {
    color: rgba(255,255,255,0.7);
}

/* Desktop search */
.desktop-search {
    position: absolute;
    top: 12px;
    right: 18px;
    width: 200px;
}

/* Hide/show based on screen size */
@media (max-width: 1023px) {
    .desktop-search {
        display: none;
    }
}

@media (min-width: 1024px) {
    .mobile-search {
        display: none;
    }
}

/* Submit button (visually hidden but accessible) */
.primary-search input[type=submit] {
    @extend .visuallyhidden;
}

/* Mobile Menu Styling */
.mm-menu {
    --mm-color-background: #a80000;
    --mm-color-text: #fff;
    --mm-color-button: #fff;
    --mm-color-border: rgba(255,255,255,0.3);
}

.mm-menu ul {
    background: var(--mm-color-background);
}

.mm-menu li {
    border-color: var(--mm-color-border);
}

/* Hide left arrows and style parent items */
.mm-menu .has_children > a:first-child {
    display: block !important;
    padding: 12px 45px 12px 20px !important;
    margin: 0 !important;
    color: var(--mm-color-text) !important;
    text-decoration: none;
}

/* Style the right arrow for submenus */
.mm-menu .has_children > a:last-child {
    position: absolute !important;
    right: 0 !important;
    top: 0 !important;
    bottom: 0 !important;
    width: 45px !important;
    padding: 0 !important;
    border-left: 1px solid var(--mm-color-border) !important;
}

.mm-menu .has_children > a:last-child:after {
    content: '';
    border: solid var(--mm-color-text);
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(-45deg);
    position: absolute;
    right: 19px;
    top: 50%;
    margin-top: -6px;
}

/* Style regular menu items */
.mm-menu a {
    color: var(--mm-color-text);
    padding: 12px 20px;
    display: block;
    text-decoration: none;
}

.mm-menu .selected > a {
    background: rgba(255,255,255,0.1);
}

/* Hide desktop nav on mobile */
@media (max-width: 1023px) {
    .desktop-nav {
        display: none;
    }
}

/* Hide mobile nav on desktop */
@media (min-width: 1024px) {
    #mobile-menu {
        display: none !important;
    }
}

/* Mega Menu Sublists */
.mega-menu__sublist {
    position: absolute;
    left: 100%;
    top: 0;
    background: #fff;
    min-width: 200px;
    max-width: 800px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 1000;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.2s;
    padding: 8px 0;
}

/* First level sublist (the 7-row grid) */
.mega-menu__item > .mega-menu__sublist {
    display: grid;
    grid-template-rows: repeat(7, auto);
    grid-auto-flow: column;
    grid-auto-columns: minmax(250px, 1fr);
    padding: 8px 0;
}

/* Second level and deeper sublists (regular dropdowns) */
.mega-menu__subitem > .mega-menu__sublist {
    display: block;
    min-width: 200px;
}

/* Show sublist on hover */
.mega-menu__item:hover > .mega-menu__sublist,
.mega-menu__subitem:hover > .mega-menu__sublist {
    visibility: visible;
    opacity: 1;
}

/* Add arrow to items with children */
.mega-menu__item:has(.mega-menu__sublist) > .mega-menu__link:after,
.mega-menu__subitem:has(.mega-menu__sublist) > .mega-menu__sublink:after {
    content: '\203A';
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    line-height: 1;
    color: #565044;
}

/* Ensure there's room for the chevron on parent items */
.mega-menu__item:has(.mega-menu__sublist) > .mega-menu__link,
.mega-menu__subitem:has(.mega-menu__sublist) > .mega-menu__sublink {
    padding-right: 30px;
}

/* Highlight parent items when child menu is hovered */
.mega-menu__item:hover > .mega-menu__link,
.mega-menu__item:has(.mega-menu__sublist:hover) > .mega-menu__link,
.mega-menu__subitem:hover > .mega-menu__sublink,
.mega-menu__subitem:has(.mega-menu__sublist:hover) > .mega-menu__sublink {
    background: #f5f5f5;
    color: #a80000;
}

/* Change arrow color on hover */
.mega-menu__item:hover > .mega-menu__link:after,
.mega-menu__item:has(.mega-menu__sublist:hover) > .mega-menu__link:after,
.mega-menu__subitem:hover > .mega-menu__sublink:after,
.mega-menu__subitem:has(.mega-menu__sublist:hover) > .mega-menu__sublink:after {
    color: #a80000;
}

/* Links styling */
.mega-menu__link,
.mega-menu__sublink {
    color: #565044;
    font-size: 14px;
    text-decoration: none;
    display: block;
    padding: 8px 12px;
    transition: background 0.3s, color 0.3s;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Hover effects */
.mega-menu__link:hover,
.mega-menu__sublink:hover {
    background: #f5f5f5;
    color: #a80000;
}

/* Ensure submenus stack properly */
.mega-menu__sublist .mega-menu__sublist {
    left: 100%;
    top: 0;
}

/* Prevent text wrapping in submenus */
.mega-menu__subitem {
    position: relative;
    white-space: nowrap;
}

/* Ensure submenus are positioned correctly */
.mega-menu__item,
.mega-menu__subitem {
    position: relative;
}

/* Secondary Navigation Hover and Active States */
.secondary-nav li:hover > .secondary-nav__dest,
.secondary-nav li:has(.mega-menu__panel:hover) > .secondary-nav__dest {
    color: #9a1e13;
    text-decoration: none;
}

.secondary-nav li:hover,
.secondary-nav li:has(.mega-menu__panel:hover) {
    background-color: #fff;
}

.secondary-nav li:hover > .secondary-nav__dest,
.secondary-nav li.is-active > .secondary-nav__dest {
    color: #9a1e13;
    text-decoration: none;
}

.secondary-nav li:hover,
.secondary-nav li.is-active {
    background-color: #fff;
}

/* Panel Display */
.secondary-nav li:hover > .mega-menu__panel {
    display: block;
}

.mm-navbar img { width: 100%; margin: 26px auto 0 auto; }

.mm-menu .has_children > a:last-child {border-left: 0 !important}

.search-toggle, .search-toggle.active {
    background: transparent;
    position: absolute;
    top: 23px;
    right: 19px;
    width: 25px;
    height: 25px;
    border: none;
    cursor: pointer;
    padding: 0;
}

.primary-search input[type=text] {
  border-radius: 2px;
  border: 0;
  height: 35px;
  width: 100%;
  padding: 12px;
}

.primary-search__input {
  position: relative;
}
.primary-search__input .button-wrap {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  width: 40px;
  align-items: center;
  justify-content: center;
}

.primary-search__input .button-wrap button {
  background: url('/img/site/icons/chevron_right_light.svg') no-repeat;
  background-position: 0 0!important;
  margin: 0;
  width: 18px;
  height: 18px;
}


.page-header { display: flex;}

.page-header__inner {
  display: flex;
  align-items: center;
  align-self: stretch;
  width: 100%;
  position: relative;
}

.page-header__logo, .desktop-menu, .primary-search--page-header, .primary-nav, .secondary-nav  {
  position: static;
}

.desktop-menu {
  display: flex;
  align-self: stretch;
  align-items: flex-start;
  flex-grow: 1; /* Allows this element to grow and fill available space */
  flex-shrink: 0; /* Allows this element to shrink if necessary */
}

.desktop-menu__inner {
  display:flex;
  flex-direction: column;
}

.primary-nav {
  padding: 0 0 7px 5px;
  display: flex;
}

.primary-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.primary-nav li {
  padding: 0 10px;
  margin: 0;
}

.primary-nav li:after { content: ""}

.secondary-nav li { transition: all 0.25s; }
.secondary-nav li a { white-space: nowrap; }

@media screen and (max-width: 1280px) {
  .secondary-nav.upper li svg, .secondary-nav li svg { display: none }
}
.page-header__logo {
  margin: 20px 18px 20px 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.page-header__logo img {
  max-width: 200px;
  height: auto;
}

@media (max-width: 1024px) {
  .page-header__logo {
    margin: 20px auto!important;
  }
  .page-header__logo img {
    /* max-width: 150px; */
  }
      .primary-search {
        padding: 0!important;
    }
  .primary-search--page-header {
    box-shadow: none!important;
    border-radius: 5px;
    width: 100% !important;
    max-width: 100%;
    border: none;
    margin: 5px 0 20px 0;
}
.primary-search__input input[type=text] {
  border-radius: 2px;
}
}

@media (max-width: 480px) {
  .page-header__logo img {
    max-width: 120px;
  }
}

@media (max-width: 1280px) {
  .page-header__inner {
      padding: 0 20px;
  }
}

@media (min-width: 1024px) {
  .secondary-nav {
      display: block;
      margin:0;
  }

  .primary-search__input .button-wrap button {
      background: url('/img/site/icons/search_grey.svg') no-repeat;
  }
}

@media (min-width: 1200px) {
  .secondary-nav li:nth-child(3) {
      display: block;
  }
}

@media (min-width: 980px) {
  .secondary-nav li {
     max-width: 100%!important;
  }
}
#mobile-menu {
    max-width: 80vw;
    max-height: 100vh
}

.mm-panels {
    overflow-y: auto!important;
    overflow-x: hidden!important;
}

.mm-navbar__title {
    outline: none;
}

@media (max-width: 1200px) {
  .secondary-nav.upper li:nth-child(3), .secondary-nav li:nth-child(3) {
    display: none;
  }
}

.desktop-nav-wrap {
    display: flex;
    width: 100%;
}

@media screen and (max-width: 1050px) {
    /* .desktop-nav-wrap {
        flex-direction: column;
    }
    .desktop-menu {
        margin: 0;
        justify-content: flex-end;
    } */
    /*  show bottom 2nd nav, hide top, align 1st nav to right */
    /* .secondary-nav.upper {
        display: none;
    } */
    /* .secondary-nav.lower {
        display: flex;
    } */
}

@media screen and (min-width: 1051px) {
.secondary-nav.upper {
        display: flex;
    }
    .secondary-nav.lower {
        display: none;
    }
}

.secondary-nav.lower {
    display: none;
}

    @media screen and (max-width: 1024px) {
    .nav-toggle, .search-toggle {
        display: block;
    }
    .primary-search--page-header {
        display: none;
    }
}

@media (max-width: 1024px) {
    .page-header__logo {
        height: 34px;
        width: 187px;
    }
}
@media (min-width: 768px) {
    .page-header {
        min-height: auto;
    }
}
