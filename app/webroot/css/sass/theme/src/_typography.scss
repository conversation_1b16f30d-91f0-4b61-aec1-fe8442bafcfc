html {
    /* font-size: 100%; */
}

p {
    margin-top: var(--spacing-24px);
    margin-bottom: var(--spacing-24px);
}

@mixin headline {
    display: block;
    margin-top: 0;
    margin-bottom: 0;
}


.headline-1 {
    @include headline;

    text-transform: uppercase;

    color: var(--headline-1-colour);
    font-weight: var(--headline-1-weight);
    font-size: var(--headline-1-text);
    line-height: var(--headline-1-line-height);
    letter-spacing: var(--headline-1-letter-spacing);

    // Add standard spacing between headline-1 and typography
    + .typography {
        margin-top: var(--headline-1-space-between-typography);
    }
}
