<?php

// Timber::$cache = true;

Timber::$dirname = [
    'views',
    'views/templates',
    'views/components/common',
];

require_once('lumberjack/bootstrap.php');

/**
 * Enqueue custom CSS and JavaScript
 */
function bonvoyage_enqueue_assets() {
    $theme_directory = get_template_directory_uri();

    // Enqueue CSS
    wp_enqueue_style('bonvoyage-custom', $theme_directory . '/assets/css/custom.css', array(), '1.0.0');
    wp_enqueue_style('bonvoyage-search-toggle', $theme_directory . '/assets/css/search-toggle-fix.css', array(), filemtime(get_template_directory() . '/assets/css/search-toggle-fix.css'));

    // Enqueue JavaScript - load in header with priority 1 to run before other scripts
    wp_enqueue_script('bonvoyage-search-toggle', $theme_directory . '/assets/js/search-toggle.js', array(), filemtime(get_template_directory() . '/assets/js/search-toggle.js'), false);
}
add_action('wp_enqueue_scripts', 'bonvoyage_enqueue_assets', 1);

/**
 * Add inline script to the head to ensure our fix runs before any other scripts
 */
function bonvoyage_add_inline_script() {
    ?>
    <script>
    // Immediate execution to fix search toggle before any other scripts run
    (function() {
        // Set a flag to indicate this script has run
        window.searchToggleFixedInline = true;

        // Only run if our main script hasn't run yet
        if (!window.searchToggleFixed) {
            console.log('[Search Toggle] Inline script initializing');

            // Function to fix the search toggle
            function fixSearchToggle() {
                // Find the search toggle button
                const searchToggle = document.querySelector('.search-toggle');

                if (searchToggle) {
                    console.log('[Search Toggle] Inline: Found search toggle button');

                    // Remove data-toggle attribute to prevent the main site's JS from handling it
                    if (searchToggle.hasAttribute('data-toggle')) {
                        console.log('[Search Toggle] Inline: Removing data-toggle attribute');
                        searchToggle.removeAttribute('data-toggle');
                    }

                    // Mark this button as fixed
                    searchToggle.setAttribute('data-search-toggle-inline-fixed', 'true');

                    // Add our click event listener - only for mobile
                    searchToggle.addEventListener('click', function(e) {
                        if (window.innerWidth < 768) {
                            e.preventDefault();
                            e.stopPropagation();

                            // Toggle active class on button
                            this.classList.toggle('active');

                            // Toggle active class on search element
                            const searchElement = document.getElementById('search');
                            if (searchElement) {
                                searchElement.classList.toggle('active');

                                // Log the current state for debugging
                                const isActive = searchElement.classList.contains('active');
                                console.log('[Search Toggle] Inline: Toggled search form visibility. Active:', isActive);

                                // Force display style if needed
                                if (isActive) {
                                    console.log('[Search Toggle] Inline: Forcing display style to block');
                                    searchElement.style.display = 'block';
                                } else {
                                    searchElement.style.display = '';
                                }
                            } else {
                                console.error('[Search Toggle] Inline: Search element not found');
                                // Try to find by class as a fallback
                                const searchByClass = document.querySelector('.primary-search');
                                if (searchByClass) {
                                    console.log('[Search Toggle] Inline: Found search form by class');
                                    searchByClass.classList.toggle('active');
                                    if (searchByClass.classList.contains('active')) {
                                        searchByClass.style.display = 'block';
                                    }
                                }
                            }
                        } else {
                            console.log('[Search Toggle] Inline: Ignoring click on desktop - search form is always visible');
                        }
                    });
                } else {
                    // Button not found yet, try again later
                    console.log('[Search Toggle] Inline: Button not found yet, will try again on DOMContentLoaded');
                }
            }

            // Try to run immediately
            fixSearchToggle();

            // Also run on DOMContentLoaded as a fallback
            document.addEventListener('DOMContentLoaded', function() {
                // Only run if not already fixed by our main script
                if (!window.searchToggleFixed) {
                    console.log('[Search Toggle] Inline: Running on DOMContentLoaded');
                    fixSearchToggle();
                }
            });
        }
    })();
    </script>
    <?php
}
// Use priority 0 to ensure this runs as early as possible
add_action('wp_head', 'bonvoyage_add_inline_script', 0);
