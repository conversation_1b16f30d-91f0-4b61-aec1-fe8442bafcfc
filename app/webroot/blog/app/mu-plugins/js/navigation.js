/* global jQuery, window, document, console, clearTimeout, setTimeout, $ */
/* jshint esversion: 5, browser: true, jquery: true, strict: true */

/**
 * BV Navigation System
 * Handles mobile and desktop navigation functionality
 */
(function ($, w, doc) {
    'use strict';

    // Ensure jQuery is available
    if (typeof jQuery === 'undefined') {
        throw new Error('BV Navigation requires jQuery');
    }

    // Console fallback is now set up after window is defined

    // Alias globals with proper scoping
    var window = w;
    var document = doc;
    var clearTimeout = window.clearTimeout;
    var setTimeout = window.setTimeout;
    var $ = jQuery.noConflict(true); // Ensure $ is available and avoid conflicts

    // Set up console fallback
    var console = window.console || {
        log: function () { /* noop */ },
        error: function () { /* noop */ },
        warn: function () { /* noop */ }
    };

    // Cache commonly used elements
    var $document = $(document);
    var $window = $(window);
    var $body = $('body');

    // Configuration
    var CONFIG = {
        debug: false, // Set to false to reduce logging
        prefix: {
            general: '[Navigation]',
            mmenu: '[MMenu]',
            megamenu: '[Megamenu]'
        },
        selectors: {
            mobileMenu: '#mobile-menu',
            megaMenu: '.mega-menu',
            menuToggle: '.mmenu-trigger',
            submenuToggle: '.submenu-toggle',
            megaMenuPlaceholder: '#megamenu-placeholder'
        },
        classes: {
            initialized: 'menu-initialized',
            active: 'active',
            open: 'open',
            hasSubmenu: 'has-submenu',
            hasMobileMenu: 'has-mobile-menu',
            hasMegaMenu: 'has-mega-menu'
        },
        timeouts: {
            menuLoadDelay: 100,
            megaMenuInitDelay: 200
        },
        endpoints: {
            mobileMenu: '/mmenu',
            megaMenu: '/megamenu'
        }
    };

    /**
     * Logger utility for consistent logging
     * @namespace
     */
    var logger = {
        /**
         * Log a message to the console
         * @param {string} message - The message to log
         * @param {*} [data] - Optional data to log
         * @param {string} [type='general'] - The type of log (general, mmenu, megamenu)
         */
        log: function(message, data, type = 'general') {
            if (!CONFIG.debug) {
                return;
            }
            const prefix = CONFIG.prefix[type] || CONFIG.prefix.general;
            console.log(prefix + ' ' + message, data || '');
        },

        /**
         * Log an error to the console - always shown regardless of debug setting
         * @param {string} message - The error message
         * @param {Error} [error] - Optional error object
         * @param {string} [type='general'] - The type of log (general, mmenu, megamenu)
         */
        error: function(message, error, type = 'general') {
            const prefix = CONFIG.prefix[type] || CONFIG.prefix.general;
            console.error(prefix + ' ERROR: ' + message, error || '');
        },

        /**
         * Log a warning to the console
         * @param {string} message - The warning message
         * @param {*} [data] - Optional data to log
         * @param {string} [type='general'] - The type of log (general, mmenu, megamenu)
         */
        warn: function(message, data, type = 'general') {
            if (!CONFIG.debug) {
                return;
            }
            const prefix = CONFIG.prefix[type] || CONFIG.prefix.general;
            console.warn(prefix + ' WARNING: ' + message, data || '');
        },

        /**
         * Log important information regardless of debug setting
         * @param {string} message - The message to log
         * @param {*} [data] - Optional data to log
         * @param {string} [type='general'] - The type of log (general, mmenu, megamenu)
         */
        important: function(message, data, type = 'general') {
            const prefix = CONFIG.prefix[type] || CONFIG.prefix.general;
            console.log(prefix + ' ' + message, data || '');
        }
    };

    /**
     * Menu utilities
     * @namespace
     */
    var MenuUtils = {
        /**
         * Log the current menu state for debugging
         */
        logState: function() {
            if (!CONFIG.debug) {
                return;
            }

            var state = {
                mobileMenu: {
                    exists: $(CONFIG.selectors.mobileMenu).length > 0,
                    initialized: $(CONFIG.selectors.mobileMenu).hasClass('mm-wrapper')
                },
                megaMenu: {
                    exists: $(CONFIG.selectors.megaMenuPlaceholder).length > 0,
                    loaded: $(CONFIG.selectors.megaMenuPlaceholder).children().length > 0
                }
            };

            logger.log('Menu state:', state);
        },

        /**
         * Check if an element exists and is visible
         * @param {jQuery} $el - The element to check
         * @returns {boolean} True if element exists and is visible
         */
        isVisible: function($el) {
            return $el && $el.length && $el.is(':visible');
        }
    };

    /**
     * Mobile Menu functionality
     * @namespace
     */
    var MobileMenu = {
        /**
         * Initialize the mobile menu
         * @param {jQuery} [$menu] - Optional menu element
         * @returns {boolean} True if initialization was successful
         */
        initializeMobileMenu: function($menu) {
            'use strict';
            var $mobileMenu = $menu || $(CONFIG.selectors.mobileMenu);
            if (!$mobileMenu.length) {
                logger.warn('Mobile menu element not found');
                return false;
            }

            // Check if mmenu is already initialized
            if ($mobileMenu.hasClass('mm-menu')) {
                logger.log('Mobile menu already initialized');
                return true;
            }

            // Check if mmenu constructor is available
            if (typeof window.Mmenu === 'undefined') {
                // Try lowercase mmenu as a fallback
                if (typeof window.mmenu === 'undefined') {
                    logger.warn('Mmenu plugin not loaded');
                    return false;
                }
            }

            try {
                // Use the appropriate constructor (Mmenu or mmenu)
                var MmenuConstructor = window.Mmenu || window.mmenu;
                var menu = new MmenuConstructor("#mobile-menu", {
                    "offCanvas": {
                        "position": "left",
                        "onClick": {
                            "close": false
                        }
                    },
                    "scrollBugFix": { "fix": true },
                    "theme": "light",
                    "slidingSubmenus": true,
                    "extensions": [
                        "pagedim-black",
                    ],
                    "navbar": {
                        "title": "Menu",
                        "titleLink": "parent"
                    },
                    "navbars": [
                        {
                            "content": [
                                '<a href="/" style="display: block; text-align: center;"><img src="/img/site/sprites/logos/bv-logo-red.svg" alt="Bon Voyage"></a>',
                            ],
                            "position": "top"
                        },
                        {
                            "position": "top",
                            "content": [
                                "prev",
                                "title",
                            ]
                        }
                    ],
                    "hooks": {
                        "openPanel:before": function($panel) {
                            // Remove aria-hidden from all panels when opening
                            document.querySelectorAll('.mm-panel').forEach(panel => {
                                panel.removeAttribute('aria-hidden');
                            });
                        },
                        "closePanel:after": function($panel) {
                            // Use inert attribute instead of aria-hidden when closing
                            document.querySelectorAll('.mm-panel:not(.mm-panel--opened)').forEach(panel => {
                                panel.setAttribute('inert', '');
                            });
                            document.querySelector('.mm-panel--opened')?.removeAttribute('inert');
                        },
                        "close:after": function() {
                            // Reset hamburger/cross icons when menu is closed
                            const trigger = document.querySelector('.mmenu-trigger');
                            if (trigger) {
                                trigger.classList.remove('active');
                                const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                                const crossIcon = trigger.querySelector('.cross-icon');
                                if (hamburgerIcon && crossIcon) {
                                    hamburgerIcon.style.display = 'block';
                                    crossIcon.style.display = 'none';
                                }
                            }
                        }
                    }
                });

                var api = menu.API;

                // Store the API for later use
                this.mmenuAPI = api;

                // Toggle hamburger/cross icon when menu opens/closes
                api.bind('open:start', function() {
                    $('.mmenu-trigger .hamburger-icon').hide();
                    $('.mmenu-trigger .cross-icon').show();
                });

                api.bind('close:start', function() {
                    $('.mmenu-trigger .cross-icon').hide();
                    $('.mmenu-trigger .hamburger-icon').show();
                });

                logger.log('Mmenu initialized successfully', { menu: menu, api: api }, 'mmenu');
                $document.trigger('mmenu:initialized');
                return true;
            } catch (error) {
                logger.error('Failed to initialize mobile menu', error);
                return false;
            }
        },

        /**
         * Load mobile menu content via AJAX
         * @param {string} [selector] - Optional selector for the menu
         * @returns {jQuery.Deferred} jQuery promise
         */
        load: function(selector) {
            selector = selector || CONFIG.selectors.mobileMenu;
            var $mobileMenu = $(selector);
            var deferred = $.Deferred();
            var self = this;

            if (!$mobileMenu.length) {
                var error = new Error('Mobile menu element not found');
                logger.error(error.message);
                return deferred.reject(error).promise();
            }

            // Check if content is already loaded
            if ($mobileMenu.children().length > 0) {
                logger.important('Mobile menu content already loaded');
                return deferred.resolveWith($mobileMenu).promise();
            }

            // Check if navigation-main.js is handling the mmenu loading
            if (window.BV_NAV && (window.BV_NAV.mmenuLoading === true || window.BV_NAV.mmenuInitialized === true)) {
                logger.important('Mobile menu is being loaded or has been initialized by navigation-main.js, skipping duplicate fetch', null, 'mmenu');
                return deferred.resolveWith($mobileMenu).promise();
            }

            // Always log endpoint requests, regardless of debug setting
            console.log('[MMenu] ENDPOINT: Loading mobile menu from ' + CONFIG.endpoints.mobileMenu);

            return $.ajax({
                url: CONFIG.endpoints.mobileMenu,
                method: 'GET',
                dataType: 'html'
            })
                .done(function(response) {
                    console.log('[MMenu] ENDPOINT: Mobile menu content loaded successfully from ' + CONFIG.endpoints.mobileMenu);
                    $mobileMenu.html(response);
                    self.initializeMobileMenu($mobileMenu);
                    logger.important('Mobile menu initialized', null, 'mmenu');
                    $document.trigger('mobileMenu:loaded');
                    deferred.resolveWith($mobileMenu, [response]);
                })
                .fail(function(xhr, status, error) {
                    var err = new Error('Failed to load mobile menu: ' + error);
                    console.error('[MMenu] ENDPOINT ERROR: ' + err.message + ' (' + CONFIG.endpoints.mobileMenu + ')');
                    deferred.rejectWith($mobileMenu, [xhr, status, err]);
                });
        }
    };

    /**
     * Mega Menu functionality
     * @namespace
     */
    var MegaMenu = {
        // Store dropdown triggers and panels
        triggers: [],
        dropdowns: [],

        /**
         * Initialize the mega menu
         */
        initialize: function() {
            var self = this;

            // Check if already initialized by navigation-main.js
            if (window.BV_NAV && window.BV_NAV.megaMenuInitialized) {
                logger.important('MegaMenu: Already initialized by navigation-main.js, skipping initialization');

                // Still trigger the event for any code that might be listening
                $document.trigger('megaMenu:initialized');
                return;
            }

            // Find all dropdown triggers and panels
            this.triggers = $('.secondary-nav__dest[data-dropdown]').toArray();
            this.dropdowns = $('.mega-menu__panel').toArray();

            logger.important('MegaMenu: Initializing with ' + this.triggers.length + ' triggers and ' + this.dropdowns.length + ' dropdowns');

            // Set up event handlers for each trigger
            this.triggers.forEach(function(trigger) {
                var $trigger = $(trigger);
                var dropdownId = $trigger.data('dropdown');
                var $dropdown = $('#' + dropdownId);

                if (!$dropdown.length) {
                    logger.warn('MegaMenu: Dropdown not found for trigger:', dropdownId);
                    return;
                }

                if ($dropdown.length) {
                    // Add mouseenter event to trigger
                    $trigger.on('mouseenter', function() {
                        self.openDropdown($dropdown[0], trigger);
                    });

                    // Add mouseleave event to trigger's parent li
                    $trigger.closest('li').on('mouseleave', function(e) {
                        // Only close if not moving to the dropdown
                        if (!e.relatedTarget || !$(e.relatedTarget).closest('#' + dropdownId).length) {
                            self.closeDropdown($dropdown[0], trigger);
                        }
                    });

                    // Add mouseleave event to dropdown
                    $dropdown.on('mouseleave', function(e) {
                        // Only close if not moving to the trigger
                        if (!e.relatedTarget || !$(e.relatedTarget).closest('[data-dropdown="' + dropdownId + '"]').length) {
                            self.closeDropdown($dropdown[0], trigger);
                        }
                    });
                }
            });

            // Set global flag for other scripts to check
            if (window.BV === undefined) {
                window.BV = {};
            }
            if (window.BV.Navigation === undefined) {
                window.BV.Navigation = {};
            }
            window.BV.Navigation.megaMenuInitialized = true;

            logger.important('MegaMenu: Initialization complete', null, 'megamenu');
            $document.trigger('megaMenu:initialized');
        },

        /**
         * Open a dropdown
         * @param {HTMLElement} dropdown - The dropdown to open
         * @param {HTMLElement} trigger - The trigger element
         */
        openDropdown: function(dropdown, trigger) {
            // Close all other dropdowns first
            this.closeAllDropdowns();

            // Show this dropdown
            $(dropdown).addClass('is-active').css('display', 'block');

            // Add active class to parent li
            $(trigger).closest('li').addClass('is-active');
        },

        /**
         * Close a dropdown
         * @param {HTMLElement} dropdown - The dropdown to close
         * @param {HTMLElement} trigger - The trigger element
         * @param {HTMLElement} [parentListItem] - The parent list item
         */
        closeDropdown: function(dropdown, trigger, parentListItem) {
            $(dropdown).removeClass('is-active').css('display', 'none');

            // Remove active class from parent li
            if (parentListItem) {
                $(parentListItem).removeClass('is-active');
            } else if (trigger) {
                $(trigger).closest('li').removeClass('is-active');
            }
        },

        /**
         * Close all dropdowns
         */
        closeAllDropdowns: function() {
            var self = this;
            this.dropdowns.forEach(function(dropdown) {
                var triggerId = dropdown.id;
                var trigger = document.querySelector('[data-dropdown="' + triggerId + '"]');
                var parentListItem = trigger ? trigger.closest('.secondary-nav li') : null;
                self.closeDropdown(dropdown, trigger, parentListItem);
            });
        },

        /**
         * Load mega menu content via AJAX
         * @param {string} [selector] - Optional selector for the menu
         * @returns {jQuery.Deferred} jQuery promise
         */
        load: function(selector) {
            var self = this;
            selector = selector || CONFIG.selectors.megaMenuPlaceholder;
            var $megaMenu = $(selector);
            var deferred = $.Deferred();

            if (!$megaMenu.length) {
                var error = new Error('Mega menu placeholder not found');
                logger.error(error.message);
                return deferred.reject(error).promise();
            }

            // Check if content is already loaded
            if ($megaMenu.children().length > 0) {
                logger.important('Mega menu content already loaded by navigation-main.js, skipping duplicate fetch', null, 'megamenu');

                // Initialize the megamenu
                setTimeout(function() {
                    self.initialize();
                }, 100);

                return deferred.resolveWith($megaMenu).promise();
            }

            // Check if we have a global flag indicating navigation-main.js is handling the load
            if (window.BV_NAV && window.BV_NAV.megaMenuLoading === true) {
                logger.important('Mega menu is being loaded by navigation-main.js, skipping duplicate fetch', null, 'megamenu');

                // Set a timeout to prevent infinite waiting
                var maxWaitTime = 5000; // 5 seconds
                var startTime = Date.now();

                // Wait for the content to be loaded by navigation-main.js
                var checkInterval = setInterval(function() {
                    // Check if content is loaded
                    if ($megaMenu.children().length > 0) {
                        clearInterval(checkInterval);

                        logger.important('Mega menu content loaded by navigation-main.js detected');

                        // Initialize the megamenu
                        setTimeout(function() {
                            self.initialize();
                        }, 100);

                        deferred.resolveWith($megaMenu);
                        return;
                    }

                    // Check if we've waited too long
                    if (Date.now() - startTime > maxWaitTime) {
                        clearInterval(checkInterval);

                        logger.error('Timed out waiting for navigation-main.js to load megamenu content');

                        // Fall back to loading it ourselves
                        logger.important('Falling back to loading megamenu content ourselves');

                        $.ajax({
                            url: CONFIG.endpoints.megaMenu,
                            method: 'GET',
                            dataType: 'html'
                        })
                            .done(function(response) {
                                logger.important('Fallback: Mega menu content loaded successfully', null, 'megamenu');
                                $megaMenu.html(response);

                                // Initialize the megamenu
                                setTimeout(function() {
                                    self.initialize();
                                }, 100);

                                deferred.resolveWith($megaMenu, [response]);
                            })
                            .fail(function(xhr, status, error) {
                                var err = new Error('Fallback: Failed to load mega menu: ' + error);
                                logger.error(err.message);
                                deferred.rejectWith($megaMenu, [xhr, status, err]);
                            });
                    }
                }, 100);

                return deferred.promise();
            }

            // Always log endpoint requests, regardless of debug setting
            console.log('[Megamenu] ENDPOINT: Loading mega menu from ' + CONFIG.endpoints.megaMenu);

            return $.ajax({
                url: CONFIG.endpoints.megaMenu,
                method: 'GET',
                dataType: 'html'
            })
                .done(function(response) {
                    console.log('[Megamenu] ENDPOINT: Mega menu content loaded successfully from ' + CONFIG.endpoints.megaMenu);
                    $megaMenu.html(response);

                    // Initialize the megamenu
                    setTimeout(function() {
                        self.initialize();
                    }, 100);

                    $document.trigger('megaMenu:loaded');
                    deferred.resolveWith($megaMenu, [response]);
                })
                .fail(function(xhr, status, error) {
                    var err = new Error('Failed to load mega menu: ' + error);
                    console.error('[Megamenu] ENDPOINT ERROR: ' + err.message + ' (' + CONFIG.endpoints.megaMenu + ')');
                    deferred.rejectWith($megaMenu, [xhr, status, err]);
                });
        }
    };

    /**
     * Event handlers for menu interactions
     * @namespace
     */
    var EventHandlers = {
        /**
         * Initialize all event listeners
         */
        init: function() {
            var self = this;

            // Toggle mobile menu
            $(document).on('click', CONFIG.selectors.menuToggle, function(e) {
                e.preventDefault();
                self.toggleMenu(e);
            });

            // Toggle submenus
            $(document).on('click', CONFIG.selectors.submenuToggle, function(e) {
                e.preventDefault();
                self.toggleSubmenu($(e.currentTarget));
            });

            // Window resize handler
            var resizeTimer;
            $window.on('resize', function() {
                if (typeof clearTimeout === 'function') {
                    clearTimeout(resizeTimer);
                }
                resizeTimer = setTimeout(function handleResizeTimeout() {
                    MenuUtils.logState();
                }, 250);
            });
        },

        /**
         * Toggle menu open/close
         * @param {Event} e - The click event
         */
        toggleMenu: function(e) {
            var $toggle = $(e.currentTarget);
            var $menu = $($toggle.attr('href') || $toggle.data('target'));

            if (!$menu.length) {
                logger.warn('Menu not found for toggle');
                return;
            }

            $menu.toggleClass(CONFIG.classes.open);
            $toggle.toggleClass(CONFIG.classes.active);
            $body.toggleClass('menu-open');
        },

        /**
         * Toggle submenu open/close
         * @param {jQuery} $toggle - The toggle element
         */
        toggleSubmenu: function($toggle) {
            var $submenu = $toggle.next('ul, .sub-menu').first();

            if (!$submenu.length) {
                logger.warn('Submenu not found for toggle');
                return;
            }

            $toggle.toggleClass(CONFIG.classes.active);
            $submenu.slideToggle(200);
        }
    };

    /**
     * Main initialization function
     */
    function init() {
        // Initialize event handlers
        EventHandlers.init();

        // Set up responsive behavior
        setupResponsiveBehavior();

        logger.important('Initializing navigation system');

        // Load mobile menu if element exists
        if ($(CONFIG.selectors.mobileMenu).length) {
            // Check if mmenu is already initialized by navigation-main.js
            if (window.BV_NAV && window.BV_NAV.mmenuInitialized) {
                logger.important('Mobile menu already initialized by navigation-main.js, skipping initialization');
            }
            // Check if mmenu is already initialized by class
            else if ($(CONFIG.selectors.mobileMenu).hasClass('mm-menu')) {
                logger.important('Mobile menu already initialized');
            } else {
                MobileMenu.load().fail(function(_, __, error) {
                    logger.error('Mobile menu failed to load', error);
                });
            }
        }

        // Load mega menu if element exists and not already initialized by navigation-main.js
        if ($(CONFIG.selectors.megaMenuPlaceholder).length) {
            // Check if already initialized by navigation-main.js
            if (window.BV_NAV && window.BV_NAV.megaMenuInitialized) {
                logger.important('Skipping MegaMenu initialization - already initialized by navigation-main.js', null, 'megamenu');
            } else {
                MegaMenu.load().fail(function(_, __, error) {
                    logger.error('Mega menu failed to load', error);
                });
            }
        } else {
            logger.log('Megamenu placeholder not found, skipping megamenu initialization');
        }

        // Log initial state if debug is enabled
        MenuUtils.logState();

        // Mark as initialized
        $body.addClass(CONFIG.classes.initialized);
        logger.important('Navigation system initialized', null, 'general');
    }

    /**
     * Set up responsive behavior for menus
     */
    function setupResponsiveBehavior() {
        // Add media query listener for desktop/mobile switching
        var mediaQuery = window.matchMedia('(min-width: 1024px)');

        // Function to handle viewport changes
        function handleViewportChange(e) {
            if (e.matches) {
                // Desktop view - close mobile menu if open
                logger.log('Switching to desktop view');

                var $mobileMenu = $(CONFIG.selectors.mobileMenu);
                if ($mobileMenu.length && MobileMenu.mmenuAPI) {
                    try {
                        MobileMenu.mmenuAPI.close();
                        logger.log('Mobile menu closed for desktop view');
                    } catch (error) {
                        logger.error('Failed to close mobile menu:', error);
                    }
                }
            } else {
                // Mobile view - close any open megamenu dropdowns
                logger.log('Switching to mobile view');
                if (MegaMenu.closeAllDropdowns) {
                    MegaMenu.closeAllDropdowns();
                }
            }
        }

        // Check initial state
        handleViewportChange(mediaQuery);

        // Add listener for changes
        try {
            // Modern browsers
            if (typeof mediaQuery.addEventListener === 'function') {
                mediaQuery.addEventListener('change', handleViewportChange);
            } else if (typeof mediaQuery.addListener === 'function') {
                // Fallback for older browsers
                // Note: This is deprecated but kept for backward compatibility
                mediaQuery.addListener(handleViewportChange);
            } else {
                logger.error('No supported media query listener method available');
            }
        } catch (err) {
            logger.error('Could not add media query listener:', err);
        }
    }

    // This initialization is now handled by the initialized flag below

    // Public API
    window.BV = window.BV || {};
    window.BV.Navigation = {
        MobileMenu: MobileMenu,
        MegaMenu: MegaMenu,
        init: init,
        logger: logger
    };

    // Initialize event listeners for menu events
    $document
        .on('menu:initialized', function () {
            logger.important('Menu initialization complete');
        })
        .on('mobileMenu:initialized', function handleMmenuInitialized() {
            logger.important('Mobile menu initialized');
            // Additional initialization code here
        })
        .on('megaMenu:initialized', function handleMegamenuInitialized() {
            logger.important('Mega menu initialized');
            // Additional initialization code here
        });

    // Initialize only once when DOM is ready
    var initialized = false;
    $document.ready(function () {
        if (!initialized) {
            initialized = true;

            // Check if navigation-main.js has already loaded or is loading the menus
            if (window.BV_NAV) {
                logger.important('Found BV_NAV from navigation-main.js:', window.BV_NAV, 'general');

                // Check mobile menu initialization status
                if (window.BV_NAV.mmenuInitialized) {
                    logger.important('Mobile menu already initialized by navigation-main.js, will skip initialization', null, 'mmenu');
                }

                // Check megamenu initialization status
                if (window.BV_NAV.megaMenuLoaded) {
                    logger.important('Megamenu already loaded by navigation-main.js', null, 'megamenu');
                } else if (window.BV_NAV.megaMenuLoading) {
                    logger.important('Megamenu is being loaded by navigation-main.js', null, 'megamenu');
                }

                if (window.BV_NAV.megaMenuInitialized) {
                    logger.important('Megamenu already initialized by navigation-main.js, will skip initialization', null, 'megamenu');
                }
            } else {
                logger.important('BV_NAV not found, navigation-main.js may not have initialized yet', null, 'general');
            }

            init();
        } else {
            logger.important('Navigation already initialized, skipping duplicate initialization');
        }
    });

    // Export public API
    var publicAPI = {
        init: init,
        MobileMenu: MobileMenu,
        MegaMenu: MegaMenu,
        logger: logger,
        utils: MenuUtils
    };

    // Make API globally available
    if (typeof window !== 'undefined') {
        window.BV = window.BV || {};
        window.BV.Navigation = publicAPI;

        // Also expose MegaMenu directly for compatibility with existing code
        window.MegaMenu = MegaMenu;
    }

    return publicAPI;
}(jQuery, window, document));
