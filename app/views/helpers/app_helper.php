<?php

class App<PERSON><PERSON><PERSON> extends Helper {

    var $helpers = array('Html');

    function __construct() {
        parent::__construct();
        $this->Html = ClassRegistry::getObject('Html');
    }

    function button($title, $url = null, $htmlAttributes = array(), $confirmMessage = false, $escapeTitle = true)
    {
        $htmlAttributes['class'] .= ' button';
        return $this->Html->link($this->Html->tag('span', $title, array(), true), $url, $htmlAttributes, $confirmMessage, false);
    }

    function javascript($javascript)
    {
        $view =& ClassRegistry::getObject('view');
        $code = "<script type=\"text/javascript\">$javascript</script>";
        $view->addScript($code);
    }

    function javascriptArray($items)
    {
        return "['".implode("','", $items)."']";
    }

    function first_last_alt($i, $count)
    {
        $className = '';
        $className .= (++$i == 1) ? ' first-child' : '';
        $className .= ($i == $count) ? ' last-child' : '';
        $className .= ($i%2) ? '' : ' alt';
        return $className;
    }

    function cssClasses($classes)
    {
        return join(' ', array_filter($classes));
    }

    function echoIf($content = null, $tag = null, $class = '')
    {
        if (!$content) {
            return;
        }
        if ($content == '<br />' || $content == '<p/>') {
            return;
        }
        $out = $content;
        if ($tag) {
            if (!empty($class)) {
                $class = ' class="'.$class.'"';
            }
            $out = sprintf('<%1$s%2$s%s>%3$s</%1$s>', $tag, $class, $out);
        }
        return $out;
    }

    function getMaxUploadSizeInBytes()
    {
        $maxUploadSize = min(ini_get('upload_max_filesize'), ini_get('post_max_size'));
        return $this->sizeInBytes($maxUploadSize);
    }

    function sizeInBytes($size) {
        $value = substr($size, 0, -1);
        $units = substr($size, -1);
        switch ($units) {
            case 'K':
            return $value * 1024;
            case 'M':
            return $value * 1024 * 1024;
            case 'G':
            return $value * 1024 * 1024 * 1024;
            case 'T':
            return $value * 1024 * 1024 * 1024 * 1024;
            case 'B':
            default:
            return $value;
            break;
        }
    }

    function getMaxImageSizeInPixels()
    {
        $memoryLimit = ini_get('memory_limit');
        $memoryLimitInBytes = $this->sizeInBytes($memoryLimit);
        $totalPixels = $memoryLimitInBytes / 5;
        $eachDimension = sqrt($totalPixels);
        $eachDimensionToLowestThousand = round($eachDimension - ($eachDimension%1000), -3);
        return $eachDimensionToLowestThousand . ' - ' . $eachDimensionToLowestThousand;
    }

    function previousPage()
    {
        if ($previousPage = ClassRegistry::getObject('view')->getVar('previousPage')) {
            return $this->Html->link($previousPage['title'], $previousPage['uri']);
        }
        return null;
    }


    function menu($items, $options = null)
    {
        $itemCount = count($items);

        if (!$itemCount) {
            return;
        }

        $out = '<ul>';

        $itemCounter = 1;

        foreach ($items as $item) {
            $liClasses = array();
            if (isset($item['selected']) && $item['selected']) {
                $liClasses[] = 'selected';
            }
            if (isset($item['has_children']) && $item['has_children']) {
                $liClasses[] = 'has_children';
            }
            if ($itemCounter == 1) {
                $liClasses[] = 'first_child';
            }
            if ($itemCounter == $itemCount) {
                $liClasses[] = 'last_child';
            }
            $out .= '<li';
            if (!empty($liClasses)) {
                $out .= ' class="'.implode(' ', $liClasses).'"';
            }
            if (isset($options['id']) && $options['id'] == true) {
                $out .= ' id="';
                if (isset($options['id_prefix'])) {
                    $out .= $options['id_prefix'];
                }
                $out .= $item['id'];
                $out .= '"';
            }
            $out .= '>';

            if (isset($options['hideParents']) && $options['hideParents'] === true) {
                if (!empty($item['children'])) {
                    $out .= $this->menu($item['children'], $options);
                } else {
                    $out .= $this->_navigationLink($item, $options);
                }
            } else {
                if (!empty($options['noLeftArrow']) && isset($item['has_children']) && $item['has_children']) {
                    $out .= $this->Html->link($item['text'], $item['url'], null, null, false);
                } else {
                    $out .= $this->_navigationLink($item, $options);
                }

                if (isset($item['children'])) {
                    $out .= $this->menu($item['children'], $options);
                }
            }

            $out .= '</li>';
            $itemCounter++;
        }

        $out .= "</ul>";

        return $out;
    }

    public function select($items, $options)
    {
        if (empty($items))
        {
            return;
        }


        $items = $this->_selectItems($items, $options);

        $items = implode('', $items);

        return '<select>' . $items . '</select>';
    }

    private function _navigationLink($item, $options = array())
    {
        if (!isset($item['url']) || $item['url'] == false) {
            return $item['text'];
        }

        if (is_array($item['url']) && isset($options['url'])) {
            $item['url'] = array_merge($options['url'], $item['url']);
        }

        return $this->Html->link($item['text'], $item['url'], null, null, false);
    }

    private function _selectItems($items, $options = array())
    {
        $selectItems = array();

        foreach ($items as $i => $item)
        {
            if (!isset($options['hideParents']) ||
            $options['hideParents'] !== true ||
            empty($item['children']))
            {
                $selectItems[] = $this->_option($item, $options);
            }

            if (!empty($item['children']))
            {
                $selectItems = array_merge($selectItems, $this->_selectItems($item['children'], $options));
            }
        }

        return $selectItems;
    }

    private function _option($item, $options = array())
    {
        if (!isset($item['url']) || $item['url'] == false) {
            return;
        }

        if (is_array($item['url']) && isset($options['url'])) {
            $item['url'] = array_merge($options['url'], $item['url']);
        }

        $url =  Router::url($item['url']);

        return '<option value="' . $url . '">' . $item['text'] . '</option>';
    }

    function stars($stars)
    {
        return strtolower(str_replace('.', '_', implode('_', array_reverse(explode(' ', $stars)))));
    }

    public function map_js_config($element, $mapData)
    {
        $mapStr = "var App = window.App||{}; App.gmaps = App.gmaps||[]; App.gmaps.push(function() {";

            if (isset($mapData['markers'])) {
                $markers = $mapData['markers'];

                unset($mapData['markers']);
            }

            if (isset($mapData['type'])) {
                $type = $mapData['type'];

                unset($mapData['type']);
            }

            $mapStr .= "var map = new App.Map('" . $element . "', ".json_encode($mapData).");";

            if ($type == 'path') {
                $mapStr .= 'map.addPath('.json_encode($markers).');';
            } else {
                foreach ($markers as $marker) {
                    $mapStr .= "map.addMarker(".json_encode($marker).");";
                }
            }

            $mapStr .= "});";

            return $mapStr;
    }

    // Converts $title to Title Case, and returns the result.
    public function strtotitle($title)
    {
        $smallwordsarray = array(
            'of','a','the','and','an','or','nor','but','is','if','then','else','when',
            'at','from','by','on','off','for','in','out','over','to','into','with');

        $words = explode(' ', $title);

        foreach ($words as $key => $word) {
            if (in_array($word, $smallwordsarray)) {
                continue;
            }

            if (preg_match('/^[A-Z]+$/', $word)) {
                continue;
            }

            $words[$key] = ucwords($word);
        }

        return implode(' ', $words);
    }

    public function toJson($value)
    {
        return htmlspecialchars(json_encode($value));
    }
}
