<button class="search-toggle" data-toggle="search"><img src="/img/site/icons/search.svg" alt="Search"></button>

<div class="primary-search <?php if (isset($modifier)) echo 'primary-search--' . $modifier ?>" id="search">
    <?php if (isset($heading) && $heading === true): ?>
        <h2>
            Search <?php if (isset($term)) { echo 'Again'; } ?>
        </h2>
    <?php endif ?>

    <form class="primary-search__form" action="/search" method="get" id="search-form">
        <fieldset>
            <button type="submit">Submit</button>

            <div class="primary-search__input">
                <input type="text" name="search" id="search-input" placeholder="Search" <?php if (isset($term)) { echo 'value="' . $term . '"'; } ?>>
            </div>

            <input type="submit" style="display:none;">
        </fieldset>
    </form>

    <script>
    (function() {
        // Immediately capture the form to prevent other scripts from interfering
        var form = document.getElementById('search-form');
        var searchInput = document.getElementById('search-input');

        if (form && searchInput) {
            // Remove any existing event listeners by cloning the form
            var newForm = form.cloneNode(true);
            form.parentNode.replaceChild(newForm, form);

            // Get references to the new elements
            var newSearchInput = document.getElementById('search-input');

            // Add our event listener with high priority
            newForm.addEventListener('submit', function(e) {
                e.preventDefault();
                e.stopImmediatePropagation();

                var searchTerm = newSearchInput.value.trim();
                if (searchTerm) {
                    // Use + for spaces instead of %20 to avoid 403 errors
                    var encodedTerm = encodeURIComponent(searchTerm).replace(/%20/g, '+');
                    window.location.href = '/search/' + encodedTerm;
                }
                return false;
            }, true); // Use capture phase for higher priority

            console.log('[Search Form] Clean URL redirect handler installed');
        }
    })();
    </script>
</div>
