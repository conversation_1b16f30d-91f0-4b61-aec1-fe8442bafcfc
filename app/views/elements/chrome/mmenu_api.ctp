<?php
// Recursive function to render the mobile menu with proper structure and classes
function renderMobileMenu($items, $level = 0) {
    if (empty($items)) return '';
    
    $html = '<ul>';
    $count = count($items);
    
    foreach ($items as $index => $item) {
        $isFirst = ($index === 0);
        $isLast = ($index === $count - 1);
        
        $classes = array();
        if ($isFirst) $classes[] = 'first_child';
        if ($isLast) $classes[] = 'last_child';
        if (!empty($item['has_children'])) $classes[] = 'has_children';
        
        $html .= '<li' . (!empty($classes) ? ' class="' . implode(' ', $classes) . '"' : '') . '>';
        $html .= '<a href="' . h($item['url']) . '">' . h($item['text']) . '</a>';
        
        if (!empty($item['has_children']) && !empty($item['children'])) {
            $html .= renderMobileMenu($item['children'], $level + 1);
        }
        
        $html .= '</li>';
    }
    
    $html .= '</ul>';
    return $html;
}

// Render the mobile menu
if (!empty($mobileNavigation)) {
    echo renderMobileMenu($mobileNavigation);
}
?>
