<script>
/**
 * Inline search toggle fix for the main site
 * This script runs immediately to fix the search toggle before any other scripts run
 */
(function() {
    // Set a flag to indicate this script has run
    window.searchToggleFixedInline = true;

    // Only run if our main script hasn't run yet
    if (!window.searchToggleFixedUniversal) {
        console.log('[Search Toggle Fix] Inline script initializing');

        // Function to fix the search toggle
        function fixSearchToggle() {
            // Find all search toggle buttons
            const searchToggles = document.querySelectorAll('.search-toggle');
            
            if (searchToggles.length > 0) {
                console.log('[Search Toggle Fix] Inline: Found ' + searchToggles.length + ' search toggle button(s)');
                
                // Fix each search toggle button
                searchToggles.forEach(function(searchToggle) {
                    // Skip if already fixed
                    if (searchToggle.hasAttribute('data-search-toggle-inline-fixed')) {
                        return;
                    }
                    
                    // Store the original data-toggle value if it exists
                    const originalToggleTarget = searchToggle.getAttribute('data-toggle');
                    
                    // CRITICAL: Remove the data-toggle attribute to prevent App.Navigation from handling it
                    if (searchToggle.hasAttribute('data-toggle')) {
                        console.log('[Search Toggle Fix] Inline: Removing data-toggle attribute');
                        searchToggle.removeAttribute('data-toggle');
                    }
                    
                    // Mark this button as fixed
                    searchToggle.setAttribute('data-search-toggle-inline-fixed', 'true');
                    
                    // Add our click event listener
                    searchToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        
                        // Toggle active class on button
                        this.classList.toggle('active');
                        
                        // Try multiple ways to find the search element
                        let searchElement = null;
                        
                        // Method 1: Use the original data-toggle value
                        if (originalToggleTarget) {
                            searchElement = document.getElementById(originalToggleTarget) || 
                                           document.querySelector('.' + originalToggleTarget);
                        }
                        
                        // Method 2: Try to find by ID 'search'
                        if (!searchElement) {
                            searchElement = document.getElementById('search');
                        }
                        
                        // Method 3: Try to find by class
                        if (!searchElement) {
                            searchElement = document.querySelector('.primary-search');
                        }
                        
                        if (searchElement) {
                            // Toggle 'active' class on the search element
                            searchElement.classList.toggle('active');
                            console.log('[Search Toggle Fix] Inline: Toggled search element visibility');
                            
                            // For mobile, ensure proper display style
                            if (window.innerWidth < 1024) {
                                if (searchElement.classList.contains('active')) {
                                    searchElement.style.display = 'block';
                                } else {
                                    // Only hide on mobile
                                    searchElement.style.display = '';
                                }
                            }
                        } else {
                            console.error('[Search Toggle Fix] Inline: Search element not found');
                        }
                    });
                });
            } else {
                // Buttons not found yet, try again later
                console.log('[Search Toggle Fix] Inline: Buttons not found yet, will try again on DOMContentLoaded');
            }
        }
        
        // Try to run immediately
        fixSearchToggle();
        
        // Also run on DOMContentLoaded as a fallback
        document.addEventListener('DOMContentLoaded', function() {
            // Only run if not already fixed by our main script
            if (!window.searchToggleFixedUniversal) {
                console.log('[Search Toggle Fix] Inline: Running on DOMContentLoaded');
                fixSearchToggle();
            }
        });
    }
})();
</script>
