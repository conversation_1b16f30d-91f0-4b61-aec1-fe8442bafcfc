<?php
if (empty($sectionData)) {
    $header = isset($sectionHeader) ? $sectionHeader : 'Bon Voyage';

    $theImage = isset($sectionImage) ? $sectionImage : '/img/site/placeholders/bon-voyage-offices.jpg';
} else {
    $header = $sectionData[$sectionModel]['name'];

    $theImage = $sectionData['MainImage'];
}

echo $this->element('modules/page_content_header', array(
    'header'   => $header
));
?>

<section class="page-content-body js-page-content-body page-content-body--destination">

<?php echo $this->element('modules/section_nav', array(
        'accommodationsContent' => $accommodationsContent,
        'activitiesContent' => $activitiesContent,
        'imagesContent' => $imagesContent,
        'itinerariesContent' => $itinerariesContent,
        'youtubeVideosContent' => $youtubeVideosContent,
    )) ?>

    <?php if (!empty($mapData)): ?>
    <div class="image-and-map__map-wrapper image-and-map__map-wrapper--fixed">
        <div class="image-and-map__map-wrapper__map ui-map-editor" id="image-and-map__map"></div>
        <button class="image-and-map__hide" type="button">Close</button>
        <noscript>
            <?php
            echo $html->image(
                implode('', array(
                    '//maps.googleapis.com/maps/api/staticmap?center=',
                    $mapData['map_latitude'],
                    ',',
                    $mapData['map_longitude'],
                    '&amp;zoom=',
                    $mapData['zoom_level'],
                    '&amp;size=355x340&amp;maptype=terrain'
                )),
                array('width' => '355', 'height' => '340'
            ));
            ?>
        </noscript>
    </div>
    <?php
        $mapStr = $app->map_js_config('image-and-map__map', $mapData);
        echo $javascript->codeBlock($mapStr, array('inline' => false));
    endif;
    ?>

    <div class="page-content-body__inner">
        <?php echo $this->element('sidebar'); ?>
    </div>


    <div class="page-content-body__inner">
        <div class="page-content-body__content js-related-content">

            <?php // see element/section_footer.ctp for footer ?>
