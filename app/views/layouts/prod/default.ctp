<!DOCTYPE html>
<html class="no-js">

<head>
    <?php
    echo $this->element('chrome/meta_tags', array(
        'title' => $title_for_layout,
        'description' => $metaDescription,
        'keywords' => $metaKeywords
    ));

    // foreach (Configure::read('GoogleMaps.api_key') as $host => $GMapsKey) {
    //     if (strpos($_SERVER['HTTP_HOST'], $host) !== false) {
    //         echo "<script>window.GMapsKey = '" . $GMapsKey . "';</script>";
    //         break;
    //     }
    // }

    echo "<script>window.GMapsKey = 'AIzaSyCNuTeVigyAWn-zYwCDourJfKS4kHVnu8I';</script>";

    echo $this->element('chrome/loadJs');
    echo $this->element('chrome/loadCss');

    if (Configure::read('debug') == 0) {
        $cssTimestamp = filemtime(WWW_ROOT . 'css/build/screen.css');
        // echo $this->element('chrome/criticalCss', array(
        //     'criticalCss' => $criticalCss
        // ));
        // echo $this->element('chrome/async_css', array(
        //     'processedCss' => ASSETS_HOSTNAME . '/css/build/screen.' . $cssTimestamp . '.css',
        // ));
        echo '<link rel="stylesheet" href="' . ASSETS_HOSTNAME . '/css/build/screen.' . $cssTimestamp . '.css' . '">';

        echo isset($additionalStyles) ? $additionalStyles : ''; ?>
    <?php } else { ?>
        <link rel="stylesheet" href="/css/build/screen.css">

        <?php echo isset($additionalStyles) ? $additionalStyles : ''; ?>
    <?php } ?>
    <link rel="stylesheet" href="/css/navigation.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mmenu-js/9.3.0/mmenu.min.css" />

    <?php echo $this->element('chrome/search_toggle_inline'); ?>
    <link rel="stylesheet" href="/css/tweaks.css?v=5">

    <script>
    (function (c, s, q, u, a, r, e) {
        c.hj=c.hj||function(){(c.hj.q=c.hj.q||[]).push(arguments)};
        c._hjSettings = { hjid: a };
        r = s.getElementsByTagName('head')[0];
        e = s.createElement('script');
        e.async = true;
        e.src = q + c._hjSettings.hjid + u;
        r.appendChild(e);
    })(window, document, 'https://static.hj.contentsquare.net/c/csq-', '.js', 5238088);
    </script>

</head>

<body>
    <div class="page-wrapper <?php echo ($this->action != 'home') ?: 'page-wrapper--home' ?> <?php echo (!empty($hideContactUsBanner) && $hideContactUsBanner === true) ? 'page-wrapper--no-banner' : '' ?>">
        <?php echo $this->element('chrome/page_header'); ?>

        <?php echo $this->element('chrome/hero') ?>

        <?php echo $this->element('chrome/breadcrumb') ?>

        <?php echo $content_for_layout; ?>

        <?php echo $this->element('chrome/page_footer'); ?>
    </div>

    <?php if (Configure::read('debug') == 0) : ?>
        <?php if ((preg_match('~MSIE|Internet Explorer~i', $_SERVER['HTTP_USER_AGENT']) === 1) || (strpos($_SERVER['HTTP_USER_AGENT'], 'Trident/7.0') !== false)) : ?>
            <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/core-js/2.6.9/core.js"></script>
        <?php endif;

        $jsTimestamp = filemtime(WWW_ROOT . 'js/build/site.js');
        ?>
        <script>
            window.loadOtherJs = () => {
                loadJS("<?php echo 'https://www.bon-voyage.co.uk/js/build/site.js' ?>", function() {
                    $$('body')[0].insert(<?php echo json_encode($scripts_for_layout) ?>);
                });

                loadJS('<?php echo ASSETS_HOSTNAME; ?>' + '/bower_components/modernizr/modernizr_build.js');
            };

            loadJS("/js/build/components.js?ver=<?php echo filemtime(WWW_ROOT . 'js/build/components.js'); ?>");
        </script>
    <?php else : ?>
        <script src="/js/build/components.js"></script>

        <script src="/bower_components/modernizr/modernizr_build.js"></script>

        <script src="/js/build/site.js"></script>

        <?php echo $scripts_for_layout ?>
    <?php endif; ?>

    <?php echo $this->element('chrome/tracking'); ?>

    <script type="text/javascript">
        var $zoho = $zoho || {};
        $zoho.salesiq = $zoho.salesiq || {
            widgetcode: 'd95938ffe2a6832100e58db2a757808689e3d8ff12421455da2686b83d2140f792bfd2045cbc83c765e85ec0ea2b18a2',
            values: {},
            ready: function() {},
        };
        var d = document;
        s = d.createElement('script');
        s.type = 'text/javascript';
        s.id = 'zsiqscript';
        s.defer = true;
        s.src = 'https://salesiq.zoho.eu/widget';
        t = d.getElementsByTagName('script')[0];
        t.parentNode.insertBefore(s, t);
        d.write("<div id='zsiqwidget'></div>");
    </script>
    <script type="text/javascript">
        (function(a, e, c, f, g, h, b, d) {
            var k = {
                ak: "1030719956",
                cl: "g2wPCMnNj3sQ1JO-6wM",
                autoreplace: "0800 316 3012"
            };
            a[c] = a[c] || function() {
                (a[c].q = a[c].q || []).push(arguments)
            };
            a[g] || (a[g] = k.ak);
            b = e.createElement(h);
            b.async = 1;
            b.src = "//www.gstatic.com/wcm/loader.js";
            d = e.getElementsByTagName(h)[0];
            d.parentNode.insertBefore(b, d);
            a[f] = function(b, d, e) {
                a[c](2, b, k, d, null, new Date, e)
            };
            a[f]()
        })(window, document, "_googWcmImpl", "_googWcmGet", "_googWcmAk", "script");
    </script>
    <script async src="https://www.google.com/recaptcha/api.js?render=6LenjsgZAAAAAH-f4aj1WPD8Rflsj57FOaAcvtkU"></script>
    <script src="/js/tweaks.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mmenu-js/9.3.0/mmenu.min.js"></script>
    <script src="/js/navigation.js"></script>
    <script src="/js/search-toggle-fix.js"></script>
    <!-- Updated -->

    <?php if (empty($hideContactUsBanner) || $hideContactUsBanner !== true) { ?>
        <div class="page-wrapper__contact-module mobile-bar">
            <div class="page-wrapper__contact-module-inner">
                <?php echo $this->element('modules/call_us') ?>
            </div>
        </div>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
          var mobileBar = document.querySelector('.page-wrapper__contact-module.mobile-bar');
          if (mobileBar && mobileBar.parentNode !== document.body) {
            document.body.appendChild(mobileBar);
            console.log('[MOBILE-BAR] Moved to body');
          }
        });
        </script>
    <?php } ?>
</body>

</html>
