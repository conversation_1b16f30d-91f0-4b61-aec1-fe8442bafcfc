<!DOCTYPE html>
<html class="no-js">

<head>
    <?php
    echo $this->element('chrome/meta_tags', array(
        'title' => $title_for_layout,
        'description' => $metaDescription,
        'keywords' => $metaKeywords
    ));

    // foreach (Configure::read('GoogleMaps.api_key') as $host => $GMapsKey) {
    //     if (strpos($_SERVER['HTTP_HOST'], $host) !== false) {
    //         echo "<script>window.GMapsKey = '" . $GMapsKey . "';</script>";
    //         break;
    //     }
    // }

    echo "<script>window.GMapsKey = 'AIzaSyCNuTeVigyAWn-zYwCDourJfKS4kHVnu8I';</script>";

    echo $this->element('chrome/loadJs');
    echo $this->element('chrome/loadCss');

    if (Configure::read('debug') == 0) {
        $cssTimestamp = filemtime(WWW_ROOT . 'css/build/screen.css');
        // echo $this->element('chrome/criticalCss', array(
        //     'criticalCss' => $criticalCss
        // ));
        // echo $this->element('chrome/async_css', array(
        //     'processedCss' => ASSETS_HOSTNAME . '/css/build/screen.' . $cssTimestamp . '.css',
        // ));
        echo '<link rel="stylesheet" href="' . ASSETS_HOSTNAME . '/css/build/screen.' . $cssTimestamp . '.css' . '">';

        echo isset($additionalStyles) ? $additionalStyles : ''; ?>
    <?php } else { ?>
        <!-- build:css /css/build/screen.css -->
        <link rel="stylesheet" href="/css/screen.css">
        <!-- endbuild -->

        <?php echo isset($additionalStyles) ? $additionalStyles : ''; ?>
    <?php } ?>
    <link rel="stylesheet" href="/css/tweaks.css?v=1">
    <link rel="stylesheet" href="/css/navigation.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mmenu-js/9.3.0/mmenu.min.css" />

    <?php echo $this->element('chrome/search_toggle_inline'); ?>
</head>

<body>
    <div class="page-wrapper <?php echo ($this->action != 'home') ?: 'page-wrapper--home' ?> <?php echo (!empty($hideContactUsBanner) && $hideContactUsBanner === true) ? 'page-wrapper--no-banner' : '' ?>">
        <?php echo $this->element('chrome/page_header'); ?>

        <?php echo $this->element('chrome/hero') ?>

        <?php echo $this->element('chrome/breadcrumb') ?>

        <?php echo $content_for_layout; ?>

        <?php echo $this->element('chrome/page_footer'); ?>

        <?php if (empty($hideContactUsBanner) || $hideContactUsBanner !== true) { ?>
            <div class="page-wrapper__contact-module mobile-bar">
                <div class="page-wrapper__contact-module-inner">
                    <?php echo $this->element('modules/call_us') ?>
                </div>
            </div>
        <?php } ?>
    </div>

    <?php if (Configure::read('debug') == 0) : ?>
        <?php if ((preg_match('~MSIE|Internet Explorer~i', $_SERVER['HTTP_USER_AGENT']) === 1) || (strpos($_SERVER['HTTP_USER_AGENT'], 'Trident/7.0') !== false)) : ?>
            <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/core-js/2.6.9/core.js"></script>
        <?php endif;

        $jsTimestamp = filemtime(WWW_ROOT . 'js/build/site.js');
        ?>
        <script>
            window.loadOtherJs = () => {
                loadJS("<?php echo ASSETS_HOSTNAME . '/js/build/site.' . $jsTimestamp . '.js' ?>", function() {
                    $$('body')[0].insert(<?php echo json_encode($scripts_for_layout) ?>);
                });

                loadJS('<?php echo ASSETS_HOSTNAME; ?>' + '/bower_components/modernizr/modernizr_build.js');
            };

            loadJS("/js/build/components.js?ver=<?php echo filemtime(WWW_ROOT . 'js/build/components.js'); ?>");
        </script>
    <?php else : ?>
        <!-- build:js /js/build/components.js -->
        <script src='/js/site/components.js'></script>
        <!-- endbuild -->

        <!-- build:js /bower_components/modernizr/modernizr_build.js -->
        <script src='/bower_components/modernizr/modernizr.js'></script>
        <!-- endbuild -->

        <!-- build:js /js/build/site.js -->
        <!-- <script src='/bower_components/prototype.js/dist/prototype.js'></script> -->
        <script src="/js/site/prototype_1.7.2-beta.js"></script>
        <!-- <script src='/bower_components/fastclick/lib/fastclick.js'></script> -->
        <script src='/bower_components/respimage/respimage.js'></script>
        <script src='/js/vendor/effects.js'></script>
        <script src='/js/vendor/h5f.min.js'></script>
        <script src='/js/vendor/marker_with_label.js'></script>
        <script src='/bower_components/SimpleSlider/src/simpleslider.js'></script>
        <script src='/bower_components/handlebars/handlebars.runtime.js'></script>
        <script src='/js/site/templates.js'></script>
        <script src='/js/site/async.js'></script>
        <script src='/js/site/carousel.js'></script>
        <script src='/js/site/video.js'></script>
        <script src='/js/site/section-tabs.js'></script>
        <script src='/js/site/section-content.js'></script>
        <script src='/js/site/navigation.js'></script>
        <script src='/js/site/map.js'></script>
        <script src='/js/site/accomm-panel.js'></script>
        <script src='/js/site/feefo-widget.js'></script>
        <script src='/js/site/itinerary-day.js'></script>
        <script src='/js/site/site.js'></script>
        <script src='/js/search-toggle-fix.js'></script>
        <!-- endbuild -->

        <?php echo $scripts_for_layout ?>
    <?php endif; ?>

    <?php echo $this->element('chrome/tracking'); ?>

    <script type="text/javascript">
        var $zoho = $zoho || {};
        $zoho.salesiq = $zoho.salesiq || {
            widgetcode: 'd95938ffe2a6832100e58db2a757808689e3d8ff12421455da2686b83d2140f792bfd2045cbc83c765e85ec0ea2b18a2',
            values: {},
            ready: function() {},
        };
        var d = document;
        s = d.createElement('script');
        s.type = 'text/javascript';
        s.id = 'zsiqscript';
        s.defer = true;
        s.src = 'https://salesiq.zoho.eu/widget';
        t = d.getElementsByTagName('script')[0];
        t.parentNode.insertBefore(s, t);
        d.write("<div id='zsiqwidget'></div>");
    </script>
    <script type="text/javascript">
        (function(a, e, c, f, g, h, b, d) {
            var k = {
                ak: "1030719956",
                cl: "g2wPCMnNj3sQ1JO-6wM",
                autoreplace: "0800 316 3012"
            };
            a[c] = a[c] || function() {
                (a[c].q = a[c].q || []).push(arguments)
            };
            a[g] || (a[g] = k.ak);
            b = e.createElement(h);
            b.async = 1;
            b.src = "//www.gstatic.com/wcm/loader.js";
            d = e.getElementsByTagName(h)[0];
            d.parentNode.insertBefore(b, d);
            a[f] = function(b, d, e) {
                a[c](2, b, k, d, null, new Date, e)
            };
            a[f]()
        })(window, document, "_googWcmImpl", "_googWcmGet", "_googWcmAk", "script");
    </script>
    <script async src="https://www.google.com/recaptcha/api.js?render=6LenjsgZAAAAAH-f4aj1WPD8Rflsj57FOaAcvtkU"></script>
    <script src="/js/tweaks.js?v=1"></script>
    <!-- Updated -->
</body>

</html>
