<?php

class Destination extends AppModel {

  var $actsAs = array('TreeCounterCache', 'OrderedHabtm', 'Publishable');

  var $name = 'Destination';

  var $validate = array(
    'slug' => array(
      'format' => array(
        'rule' => array('custom', '/^[a-z0-9_]*$/i'),
        'message' => 'Slug can only contain alpha numeric characters and the underscore',
      ),
      'unique' => array(
        'rule' => 'isUnique',
        'message' => 'There is already a record with this slug - slugs must be unique',
      )
    ),
    'youtube_playlist_id' => array(
      'format' => array(
        'rule' => '/^(PL)?([A-F0-9]){14,16}$/',
        'allowEmpty' => true,
        'message' => '16 characters, A-F (uppercase) and 0-9 - optionally prefixed with "PL"',
      )
    ),
  );

  var $belongsTo = array(
    'MainImage' => array(
      'className' => 'Image',
      'foreignKey' => 'main_image_id',
      'conditions' => '',
      'fields' => '',
      'order' => '',
      'list' => false,
      'form' => 'image_picker'
    ),
    'BannerImage' => array(
      'className' => 'Image',
      'foreignKey' => 'banner_image_id',
      'conditions' => '',
      'fields' => '',
      'order' => '',
      'list' => false,
      'form' => 'image_picker'
    )
  );

  var $hasMany = array(
    'ContentBlock' => array(
      'className' => 'ContentBlock',
      'foreignKey' => 'modelid',
      'dependent' => true,
      'conditions' => array('ContentBlock.model' => 'Destination'),
      'fields' => '',
      'order' => '`ContentBlock`.`order`',
      'limit' => '',
      'offset' => '',
      'exclusive' => '',
      'finderQuery' => '',
      'counterQuery' => '',
      'manageInEditPage' => true
    )
  );

  var $hasAndBelongsToMany = array(
    'Accommodation' => array(
      'className' => 'Accommodation',
      'joinTable' => 'accommodation_destinations',
      'foreignKey' => 'destination_id',
      'associationForeignKey' => 'accommodation_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'AccommodationDestination.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'list' => false,
      'form' => 'ordered'
    ),
    'Activity' => array(
      'className' => 'Activity',
      'joinTable' => 'activities_destinations',
      'foreignKey' => 'destination_id',
      'associationForeignKey' => 'activity_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'ActivitiesDestination.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'list' => false,
      'form' => 'ordered'
    ),
    'HolidayType1' => array(
      'className' => 'HolidayType',
      'joinTable' => 'destinations_on_holiday_types',
      'foreignKey' => 'destination_id',
      'associationForeignKey' => 'holiday_type_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'DestinationsOnHolidayType.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'form' => 'tag_picker'
    ),
    'HolidayType2' => array(
      'className' => 'HolidayType',
      'joinTable' => 'holiday_types_on_destinations',
      'foreignKey' => 'destination_id',
      'associationForeignKey' => 'holiday_type_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'HolidayTypesOnDestination.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'form' => 'ordered'
    ),
    'Image' => array(
      'className' => 'Image',
      'joinTable' => 'destinations_images',
      'foreignKey' => 'destination_id',
      'associationForeignKey' => 'image_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'DestinationsImage.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'list' => false,
      'form' => 'ordered'
    ),
    'Itinerary' => array(
      'className' => 'Itinerary',
      'joinTable' => 'destinations_itineraries',
      'foreignKey' => 'destination_id',
      'associationForeignKey' => 'itinerary_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'DestinationsItinerary.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'list' => false,
      'form' => 'ordered'
    ),
    'LandingPage' => array(
      'className' => 'LandingPage',
      'joinTable' => 'destinations_landing_pages',
      'foreignKey' => 'destination_id',
      'associationForeignKey' => 'landing_page_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'DestinationsLandingPage.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'form' => 'tag_picker'
    ),
    'Testimonial' => array(
      'className' => 'Testimonial',
      'joinTable' => 'destinations_testimonials',
      'foreignKey' => 'destination_id',
      'associationForeignKey' => 'testimonial_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'DestinationsTestimonial.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'form' => 'ordered'
    ),
  );

  function getBySlug($slug) {
    return $this->find('first', array(
      'conditions' => array(
        '`'.$this->alias.'`.`slug`' => $slug,
      ),
      'contain' => array(
        'Accommodation' => array(
            'conditions' => array('Accommodation.published' => true),
            'Image',
        ),
        'Activity' => array(
            'conditions' => array('Activity.published' => true),
            'Image',
        ),
        'BannerImage',
        'ContentBlock' => 'Image',
        'Image',
        'Itinerary' => array(
            'conditions' => array('Itinerary.published' => true),
            'Image',
        ),
        'MainImage',
        'Testimonial' => array(
            'conditions' => array('Testimonial.published' => true),
            'Image',
        ),
      )
    ));
  }

  function getDestinationsOnHolidayType($holidayTypeId) {

    $this->bindModel(array(
      'hasOne' => array(
        'DestinationsOnHolidayType'
      ),
    ), false);

    $this->recursive = 0;

    $conditions = array('DestinationsOnHolidayType.holiday_type_id' => $holidayTypeId);

    $order = 'DestinationsOnHolidayType.order';

    return $this->find('all', compact('conditions', 'order'));

  }

  function hasImages($destinationId) {
    return $this->DestinationsImage->hasAny(array('destination_id' => $destinationId));
  }

  function hasItineraries($destinationId) {
    return $this->DestinationsItinerary->hasAny(array('destination_id' => $destinationId));
  }

  function hasActivities($destinationId) {
    return $this->ActivitiesDestination->hasAny(array('destination_id' => $destinationId));
  }

  function hasAccommodation($destinationId) {
    return $this->AccommodationDestination->hasAny(array('destination_id' => $destinationId));
  }

  function hasTestimonials($destinationId) {
    return $this->DestinationsTestimonial->hasAny(array('destination_id' => $destinationId));
  }

  function getMapData($destinationId, $for = 'display') {

    $this->recursive = -1;

    $children = $this->find('all', array('conditions' => array('parent_id' => $destinationId)));

    $markers = array();

    foreach ($children as $child) {
      $marker = array(
        'id' => $child['Destination']['id'],
        'address' => $child['Destination']['name'],
      );
      if ((float)$child['Destination']['latitude']) {
        $marker['latitude'] = $child['Destination']['latitude'];
      }
      if ((float)$child['Destination']['longitude']) {
        $marker['longitude'] = $child['Destination']['longitude'];
      }
      if ($for == 'editor') {
        $marker['controller'] = 'destinations';
        $marker['model'] = 'Destination';
        $marker['draggable'] = true;
      } elseif ($for == 'display') {
        $marker['url'] = '/destinations/'.$child['Destination']['slug'];
      }
      $markers[] = $marker;
    }

    $this->id = $destinationId;

    $data = $this->find('first');

    $mapData = array(
      'id' => $data['Destination']['id'],
      'address' => $data['Destination']['name'],
      'markers' => $markers
    );
    if ($for == 'editor') {
      $mapData['controller'] = 'destinations';
      $mapData['model'] = 'Destination';
    }
    $coordData = array();

    // map longitude & latitude
    foreach (array('latitude', 'longitude') as $key => $value) {
      if ((float)$data['Destination']['map_'.$value]) {
        $coordData['map_'.$value] = $data['Destination']['map_'.$value];
      } elseif ((float)$data['Destination'][$value]) {
        $coordData['map_'.$value] = $data['Destination'][$value];
      }
    }

    // map - type, zoom & streeview data
    foreach (array('map_type', 'streetview', 'zoom_level') as $key => $value) {
      if ($data['Destination'][$value]) {
        $coordData[$value] = $data['Destination'][$value];
      }
    }

    if (count($coordData) >= 3) {
      $mapData += $coordData;
    }

    $mapData['type'] = 'markers';

    return $mapData;
  }

  public function getDestinationsByCountry($country = null) {
    // error_log("DEBUG: Getting destinations for country: " . $country);
    
    // Get parent ID based on country
    $parentId = ($country === 'USA') ? 1 : ($country === 'Canada' ? 2 : null);
    
    if (!$parentId) {
        // error_log("DEBUG: Invalid country parameter: " . $country);
        return array();
    }
    
    // Get direct children of the parent country
    $results = $this->find('all', array(
        'conditions' => array(
            'Destination.parent_id' => $parentId,
            'Destination.published' => 1,
            'NOT' => array(
                'Destination.name LIKE' => '%A-Z by%'  // Exclude A-Z listings
            )
        ),
        'fields' => array(
            'Destination.id',
            'Destination.name',
            'Destination.slug'
        ),
        'recursive' => -1,
        'order' => 'Destination.name ASC'
    ));
    
    // error_log("DEBUG: Query for " . $country . ": " . $this->getLastQuery());
    // error_log("DEBUG: Found " . count($results) . " destinations for " . $country);
    
    if (empty($results)) {
        //error_log("DEBUG: Parent ID " . $parentId . " returned no results");
    }
    
    return $results;
  }

  // Helper method to debug tree structure
  public function debugTree() {
    // Get top-level destinations first
    $topLevel = $this->find('all', array(
      'conditions' => array(
        'Destination.parent_id' => null,
        'Destination.published' => 1
      ),
      'fields' => array('id', 'name', 'slug'),
      'recursive' => -1
    ));
    
    debug("Top level destinations:");
    debug($topLevel);
    
    // Then get all published destinations
    $all = $this->find('all', array(
      'conditions' => array(
        'Destination.published' => 1
      ),
      'fields' => array('id', 'parent_id', 'name', 'slug', 'lft', 'rght'),
      'recursive' => -1,
      'order' => 'Destination.lft ASC'
    ));
    
    debug("All published destinations:");
    debug($all);
    
    return $all;
  }

  public function getNavigationDestinations() {
    $destinations = $this->find('all', array(
      'conditions' => array(
        'Destination.active' => 1,
        'Destination.parent_id' => null
      ),
      'fields' => array('id', 'name', 'slug'),
      'order' => array('Destination.name ASC'),
      'recursive' => -1
    ));
    
    // error_log("DEBUG: Destination Query SQL: " . $this->getLastQuery());
    // error_log("DEBUG: Raw Destinations Data: " . print_r($destinations, true));
    
    return $destinations;
  }
  
  public function getLastQuery() {
    try {
        $dbo = $this->getDataSource();
        if (method_exists($dbo, 'getLog')) {
            $logs = $dbo->getLog();
            $lastLog = end($logs['log']);
            return $lastLog['query'];
        }
        return "Query logging not available";
    } catch (Exception $e) {
        return "Error getting query log";
    }
  }

  public $findMethods = array('usa' => true, 'canada' => true);

  protected function _findUsa($state, $query, $results = array()) {
    if ($state === 'before') {
      if (!isset($query['conditions'])) {
        $query['conditions'] = array();
      }
      $query['conditions'] = array_merge($query['conditions'], array(
        'Destination.parent_id' => 1,
        'Destination.published' => 1,
        'NOT' => array(
          'Destination.name LIKE' => '%A-Z by%'
        )
      ));
      $query['fields'] = array('Destination.id', 'Destination.name', 'Destination.slug');
      $query['order'] = array('Destination.name' => 'ASC');
      $query['recursive'] = -1;
      return $query;
    }
    return $results;
  }

  protected function _findCanada($state, $query, $results = array()) {
    if ($state === 'before') {
      if (!isset($query['conditions'])) {
        $query['conditions'] = array();
      }
      $query['conditions'] = array_merge($query['conditions'], array(
        'Destination.parent_id' => 2,
        'Destination.published' => 1,
        'NOT' => array(
          'Destination.name LIKE' => '%A-Z by%'
        )
      ));
      $query['fields'] = array('Destination.id', 'Destination.name', 'Destination.slug');
      $query['order'] = array('Destination.name' => 'ASC');
      $query['recursive'] = -1;
      return $query;
    }
    return $results;
  }

}

?>
