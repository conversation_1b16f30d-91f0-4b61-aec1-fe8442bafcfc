<?php

class UploadBehavior extends ModelBehavior {

  var $_defaults = array(
    'extension_field' => 'extension',
    'file_field' => 'file',
    'destination_filename_format' => '%1$d.%2$s',
    'destination_path' => array(
      'webroot',
      'img',
      'uploads',
      'originals',
    ),
  );

  var $Folder;

  function setup(&$model, $config = array()) {

    if (!is_array($config)) {
      $config = array($config);
    }

    $this->settings[$model->alias] = array_merge($this->_defaults, $config);

    $this->checkSettings($model);

  }

  function checkSettings(&$model) {

    if (!ini_get('file_uploads')) {
      error_log('UploadBehavior: File uploads are not enabled, check file_uploads setting in php.ini');
    }

    if (!$model->schema($this->settings[$model->alias]['extension_field'])) {
      error_log('UploadBehavior: Extension field doesn\'t exist in model, check UploadBehavior settings');
    }

    $destinationPath = $this->getDestinationPath($model);

    if ($this->destinationPathExists($destinationPath)) {
      $this->checkDestinationPathIsWritable($destinationPath);
    }

  }

  function destinationPathExists($destinationPath) {

    if (is_dir($destinationPath)) {

      return true;

    }

    App::import('Core', 'Folder');

    $Folder = new Folder();

    if ($Folder->create($destinationPath)) {
      return true;
    }

    // Log error instead of triggering it to prevent headers already sent issues
    error_log('UploadBehavior: Destination directory [' . $destinationPath . '] doesn\'t exist and couldn\'t create it, check UploadBehavior settings or create it');

    return false;

  }

  function checkDestinationPathIsWritable($destinationPath) {

    if (is_writable($destinationPath)) {

      return true;

    }

    App::import('Core', 'Folder');

    $this->Folder = new Folder();

    if ($this->Folder->chmod($destinationPath)) {
      return true;
    }

    // Log error instead of triggering it to prevent headers already sent issues
    error_log('UploadBehavior: Destination directory [' . $destinationPath . '] isn\'t writable and couldn\'t be made writable, check directory settings');

    return false;

  }

  function beforeSave(&$model) {

    if ($this->fileInData($model)) {
      $this->setExtension($model, $this->getExtension($model, $this->getFileName($model)));
    }

    return true;

  }

  function fileInData(&$model) {

    if (!$this->getFileData($model)) {
      return false;
    }

    if ($this->getFileData($model, 'error')) {
      return false;
    }

    if (!file_exists($this->getTmpName($model))) {
      return false;
    }

    return true;

  }

  function getFileData(&$model, $field = 'name') {

    if (!$fileData = $model->data[$model->alias][$this->settings[$model->alias]['file_field']]) {
      return false;
    }

    if (!isset($fileData[$field])) {
      return false;
    }

    return $fileData[$field];

  }

  function getTmpName(&$model) {

    if (!$tmpName = $this->getFileData($model, 'tmp_name')) {
      return false;
    }

    $tmpDir = $this->getTmpDir();

    if (!stristr($tmpName, $tmpDir)) {
      $tmpName = TMP . $tmpName;
    }

    return $tmpName;

  }

  function getTmpDir() {

    if ($uploadTmpDir = ini_get('upload_tmp_dir')) {
      return $uploadTmpDir;
    }

    if ($tmp = sys_get_temp_dir()) {
      return $tmp;
    }

    trigger_error(__('System temp dir not found', true), E_ERROR);

  }

  function getFileName(&$model) {

    if ($fileName = $this->getFileData($model, 'name')) {
      return $fileName;
    }

    return null;

  }

  function getExtension(&$model, $fileName = null) {

    if (is_null($fileName)) {
      return null;
    }

    return low(end($tmp = explode('.', $fileName)));

  }

  function setExtension(&$model, $extension = '') {

    $model->data[$model->alias][$this->settings[$model->alias]['extension_field']] = $extension;

  }

  function afterSave(&$model, $created) {

    if (!$this->fileInData($model)) {
      return false;
    }

    return $this->moveTmpFile($model);

  }

  function moveTmpFile(&$model) {

    $from = $this->getTmpName($model);

    $to = $this->getDestinationPath($model) . DS . $this->getDestinationFileName($model);

    if (file_exists($to)) {
      unlink($to);
    }

    return rename($from, $to);

  }

  function getDestinationPath(&$model) {

    return APP.implode(DS, $this->settings[$model->alias]['destination_path']);

  }

  function getDestinationFileName(&$model, $id = null, $extension = null) {

    if (!$id && !($id = $model->id)) {
      return null;
    }

    if (!$extension && !($extension = $model->data[$model->alias][$this->settings[$model->alias]['extension_field']])) {
      return null;
    }

    $destinationFileName = sprintf($this->settings[$model->alias]['destination_filename_format'], $id, $extension);

    return $destinationFileName;

  }

}
?>
