<?php

class WebadminMenu extends AppModel {

  var $name = 'WebadminMenu';
  var $useTable = 'webadmin_menu';
  var $actsAs = array('TreeCounterCache');
  var $displayField = 'text';
  var $validate = array(
    'parent_id' => array(
      'numeric' => array(
        'rule' => 'numeric',
        'required' => false,
        'allowEmpty' => true,
        'on' => null,
        'message' => 'The Parent Id must be Numeric',
      ),
    ),
    'text' => array(
      'notEmpty' => array(
        'rule' => 'notEmpty',
        'required' => false,
        'allowEmpty' => false,
        'on' => null,
        'message' => 'The Text must be Not Empty',
      ),
    ),
    'aco_id' => array(
      'numeric' => array(
        'rule' => 'numeric',
        'required' => false,
        'allowEmpty' => true,
        'on' => null,
        'message' => 'The Aco Id must be Numeric',
      ),
    ),
  );

  var $belongsTo = array(
    'Aco' => array(
      'className' => 'Aco',
      'foreignKey' => 'aco_id',
      'conditions' => '',
      'fields' => '',
      'order' => '',
      'list' => false
    )
  );

  function getAcos() {

    $this->Aco->bindModel(array('belongsTo' => array('Parent' => array('className' => 'Aco'))));

    $data = $this->Aco->find('all', array(
      'conditions' => array(
        'Aco.alias' => Configure::read('Routing.admin').'_index',
      ),
      'fields' => array('Aco.id', 'Aco.parent_id', 'Parent.id', 'Parent.alias'),
      'recursive' => 0
    ));

    $acos = array();

    foreach ($data as $aco) {
      $acos[$aco['Aco']['id']] = Inflector::humanize(Inflector::underscore($aco['Parent']['alias']));
    }

    return $acos;

  }

  function afterSave() {

    $this->cacheWebadminMenu();

  }

  function afterDelete() {

    $this->cacheWebadminMenu();

  }

  function cacheWebadminMenu() {

    $this->Aco->bindModel(array(
      'belongsTo' => array(
        'Parent' => array(
          'className' => 'Aco',
          'fields' => 'Parent.id, Parent.alias'
        )
      )
    ));

    $this->Aco->unbindModel(array(
      'hasAndBelongsToMany' => array('Aro')
    ));

    $menu = $this->find('threaded', array(
      'contain' => array('Aco' => 'Parent'),
      'fields' => 'WebadminMenu.id, WebadminMenu.parent_id, WebadminMenu.text, WebadminMenu.aco_id, Aco.id, Aco.alias, Aco.parent_id',
      'order' => 'WebadminMenu.lft',
    ));

    $menu = $this->_processMenu($menu);

    cache('webadmin_menu', serialize($menu));

    return $menu;

  }

  function _processMenu($items) {

    $nodes = array();

    foreach ($items as $item) {

      $node = array();

      if (isset($item['Aco']['Parent']['alias'])) {

        $node['url'] = array(
          'controller' => Inflector::underscore($item['Aco']['Parent']['alias'])
        );
        $action = $item['Aco']['alias'];
        if (strpos($action, Configure::read('Routing.admin')) !== false) {
          $node['url'][Configure::read('Routing.admin')] = true;
          $action = str_replace(Configure::read('Routing.admin').'_', '', $action);
        }
        $node['url']['action'] = $action;

      }

      $node['text'] = $item['WebadminMenu']['text'];

      if (isset($item['children'])
      && is_array($item['children'])
      && !empty($item['children'])) {
        $node['has_children'] = true;
        $node['children'] = $this->_processMenu($item['children']);
      }

      $nodes[] = $node;

    }

    return $nodes;

  }

}

?>