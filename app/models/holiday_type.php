<?php
class HolidayType extends AppModel {

  var $name = 'HolidayType';
  var $actsAs = array('AutoFillSummary', 'Sequence.Sequence', 'OrderedHabtm', 'Publishable');

  var $validate = array(
    'slug' => array(
      'format' => array(
        'rule' => array('custom', '/^[a-z0-9_]*$/i'),
        'message' => 'Slug can only contain alpha numeric characters and the underscore',
      ),
      'unique' => array(
        'rule' => 'isUnique',
        'message' => 'There is already a record with this slug - slugs must be unique',
      )
    )
  );

  var $belongsTo = array(
    'Image' => array(
      'className' => 'Image',
      'foreignKey' => 'image_id',
      'conditions' => '',
      'fields' => '',
      'order' => '',
      'list' => false,
      'form' => 'image_picker'
    ),
    'MainImage' => array(
      'className' => 'Image',
      'foreignKey' => 'main_image_id',
      'conditions' => '',
      'fields' => '',
      'order' => '',
      'list' => false,
      'form' => 'image_picker'
    ),
    'BannerImage' => array(
      'className' => 'Image',
      'foreignKey' => 'banner_image_id',
      'conditions' => '',
      'fields' => '',
      'order' => '',
      'list' => false,
      'form' => 'image_picker'
    )
  );

  var $hasMany = array(
    'ContentBlock' => array(
      'className' => 'ContentBlock',
      'foreignKey' => 'modelid',
      'dependent' => true,
      'conditions' => array('ContentBlock.model' => 'HolidayType'),
      'fields' => '',
      'order' => '`order`',
      'limit' => '',
      'offset' => '',
      'exclusive' => '',
      'finderQuery' => '',
      'counterQuery' => '',
      'manageInEditPage' => true
    )
  );

  var $hasAndBelongsToMany = array(
    'Accommodation' => array(
      'className' => 'Accommodation',
      'joinTable' => 'accommodation_holiday_types',
      'foreignKey' => 'holiday_type_id',
      'associationForeignKey' => 'accommodation_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'AccommodationHolidayType.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'form' => 'ordered'
    ),
    'Activity' => array(
      'className' => 'Activity',
      'joinTable' => 'activities_holiday_types',
      'foreignKey' => 'holiday_type_id',
      'associationForeignKey' => 'activity_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'ActivitiesHolidayType.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'form' => 'ordered'
    ),
    'Destination1' => array(
      'className' => 'Destination',
      'joinTable' => 'holiday_types_on_destinations',
      'foreignKey' => 'holiday_type_id',
      'associationForeignKey' => 'destination_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'HolidayTypesOnDestination.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'list' => 'flat',
      'form' => 'tag_picker'
    ),
    'Destination2' => array(
      'className' => 'Destination',
      'joinTable' => 'destinations_on_holiday_types',
      'foreignKey' => 'holiday_type_id',
      'associationForeignKey' => 'destination_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'DestinationsOnHolidayType.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'list' => false,
      'form' => 'ordered'
    ),
    'Itinerary' => array(
      'className' => 'Itinerary',
      'joinTable' => 'holiday_types_itineraries',
      'foreignKey' => 'holiday_type_id',
      'associationForeignKey' => 'itinerary_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'HolidayTypesItinerary.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'form' => 'ordered'
    ),
    'LandingPage' => array(
      'className' => 'LandingPage',
      'joinTable' => 'holiday_types_landing_pages',
      'foreignKey' => 'holiday_type_id',
      'associationForeignKey' => 'landing_page_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'HolidayTypesLandingPage.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'form' => 'tag_picker'
    ),
    'Testimonial' => array(
      'className' => 'Testimonial',
      'joinTable' => 'holiday_types_testimonials',
      'foreignKey' => 'holiday_type_id',
      'associationForeignKey' => 'testimonial_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'HolidayTypesTestimonial.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'form' => 'ordered'
    ),
  );

  function getHolidayTypesOnDestination($destinationId) {

    $this->bindModel(array(
      'hasOne' => array(
        'HolidayTypesOnDestination'
      ),
    ), false);

    $this->recursive = 0;

    $conditions = array('HolidayTypesOnDestination.destination_id' => $destinationId);

    $order = 'HolidayTypesOnDestination.order';

    return $this->find('all', compact('conditions', 'order'));

  }

  function getHolidayTypesByLandingPage($landingPageId) {

    $this->bindModel(array(
      'hasOne' => array(
        'HolidayTypesLandingPage'
      ),
    ), false);

    $this->recursive = 0;

    $conditions = array('HolidayTypesLandingPage.landing_page_id' => $landingPageId);

    $order = 'HolidayTypesLandingPage.order';

    return $this->find('all', compact('conditions', 'order'));

  }

  function getBySlug($slug) {
    $data = $this->find('first', array(
      'conditions' => array(
        '`'.$this->alias.'`.`slug`' => $slug,
      ),
      'contain' => array(
        'BannerImage',
        'Accommodation' => array(
            'conditions' => array('Accommodation.published' => true),
            'Image',
        ),
        'Activity' => array(
            'conditions' => array('Activity.published' => true),
            'Image',
        ),
        'ContentBlock' => 'Image',
        'Image',
        'Itinerary' => array(
            'conditions' => array('Itinerary.published' => true),
            'Image',
        ),
        'MainImage',
        'Testimonial' => array(
            'conditions' => array('Testimonial.published' => true),
            'Image',
        ),
      )
    ));
    return $data;
  }

  function hasAccommodation($id) {
    return $this->AccommodationHolidayType->hasAny(array('holiday_type_id' => $id));
  }

  function hasActivities($id) {
    return $this->ActivitiesHolidayType->hasAny(array('holiday_type_id' => $id));
  }

  function hasDestinations($id) {
    return $this->DestinationsOnHolidayType->hasAny(array('holiday_type_id' => $id));
  }

  function hasItineraries($id) {
    return $this->HolidayTypesItinerary->hasAny(array('holiday_type_id' => $id));
  }

  function hasTestimonials($id) {
    return $this->HolidayTypesTestimonial->hasAny(array('holiday_type_id' => $id));
  }

  function getNavigation($selectedId = null) {
    // error_log("DEBUG: Getting holiday types for navigation");

    $holidayTypes = $this->find('all', array(
        'conditions' => array(
            'HolidayType.published' => 1
        ),
        'fields' => array('id', 'slug', 'name'),
        'order' => array('HolidayType.name ASC')
    ));

    // error_log("DEBUG: Found " . count($holidayTypes) . " holiday types");

    $items = array();
    foreach ($holidayTypes as $holidayType) {
        $item = array(
            'url' => array('controller' => 'holiday_types', 'action' => 'view', 'holiday_type_slug' => $holidayType['HolidayType']['slug']),
            'text' => $holidayType['HolidayType']['name'],
            'has_children' => false,
            'children' => array(),
        );
        if ($selectedId == $holidayType['HolidayType']['id']) {
            $item['selected'] = true;
        }
        $items[] = $item;
    }

    $items = array(
        array(
            'url' => array('controller' => 'holiday_types', 'action' => 'index'),
            'text' => 'Holidays',
            'has_children' => true,
            'children' => $items,
        )
    );

    if (!$selectedId) {
        $items[0]['selected'] = true;
    }

    return $items;
  }

  public function getAllTypes() {
    debug("Getting all holiday types");

    $results = $this->find('all', array(
      'fields' => array('id', 'name', 'slug'),
      'order' => 'name ASC'
    ));

    debug("Holiday types results:");
    debug($results);

    return $results;
  }

  public function getLastQuery() {
    $dbo = $this->getDatasource();
    $logs = $dbo->getLog();
    $lastLog = end($logs['log']);
    return $lastLog['query'];
  }



}

?>
