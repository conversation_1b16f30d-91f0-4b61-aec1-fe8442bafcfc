<?php

class Spotlight extends AppModel {

  var $name = 'Spotlight';
  var $actsAs = array(
    'Sequence.Sequence' => array(
      'detach_behaviors' => array('Publishable', 'Expires'),
    ),
    'Publishable',
    'Expires'
  );
  var $validate = array(
    'name' => array(
      'notEmpty' => array(
        'rule' => 'notEmpty',
        'required' => false,
        'allowEmpty' => false,
        'on' => null,
        'message' => 'The Name must be Not Empty',
      ),
    ),
    'slug' => array(
      'notEmpty' => array(
        'rule' => 'notEmpty',
        'required' => false,
        'allowEmpty' => false,
        'on' => null,
        'message' => 'The Slug must be Not Empty',
      ),
    ),
    'image_id' => array(
      'numeric' => array(
        'rule' => 'numeric',
        'required' => false,
        'allowEmpty' => true,
        'on' => null,
        'message' => 'The Image Id must be Numeric',
      ),
    ),
  );

  var $belongsTo = array(
    'Image' => array(
      'className' => 'Image',
      'foreignKey' => 'image_id',
      'conditions' => '',
      'fields' => '',
      'order' => '',
      'list' => false,
      'form' => 'image_picker'
    ),
    'BannerImage' => array(
      'className' => 'Image',
      'foreignKey' => 'banner_image_id',
      'conditions' => '',
      'fields' => '',
      'order' => '',
      'list' => false,
      'form' => 'image_picker'
    )
  );

  var $hasMany = array(
    'ContentBlock' => array(
      'className' => 'ContentBlock',
      'foreignKey' => 'modelid',
      'dependent' => true,
      'conditions' => array('ContentBlock.model' => 'Spotlight'),
      'fields' => '',
      'order' => '`ContentBlock`.`order`',
      'limit' => '',
      'offset' => '',
      'exclusive' => '',
      'finderQuery' => '',
      'counterQuery' => '',
      'manageInEditPage' => true
    )
  );

  var $hasAndBelongsToMany = array(
    'Images' => array(
      'className' => 'Image',
      'joinTable' => 'images_spotlights',
      'foreignKey' => 'spotlight_id',
      'associationForeignKey' => 'image_id',
      'unique' => true,
      'conditions' => '',
      'fields' => '',
      'order' => 'ImagesSpotlight.order',
      'limit' => '',
      'offset' => '',
      'finderQuery' => '',
      'deleteQuery' => '',
      'insertQuery' => '',
      'list' => false,
      'form' => 'ordered'
    ),
  );

  function getBySlug($slug) {
    $data = $this->find('first', array(
      'conditions' => array(
        'slug' => $slug,
      ),
      'contain' => array(
        'Image',
        'BannerImage',
        'Images',
        'ContentBlock' => 'Image'
      )
    ));
    return $data;
  }

  function getNavigation($selectedId = null) {
    $spotlights = $this->find('all', array('fields' => array('id', 'slug', 'name')));
    $navigation = $spotlights;

    $items = array();

    foreach ($spotlights as $spotlight) {

      $item = array(
        'url' => array('controller' => 'spotlights', 'action' => 'view', 'spotlight_slug' => $spotlight['Spotlight']['slug']),
        'text' => $spotlight['Spotlight']['name'],
        'has_children' => false,
        'children' => array(),
      );
      if ($selectedId == $spotlight['Spotlight']['id']) {
        $item['selected'] = true;
      }
      $items[] = $item;

    }

    return $items;
  }

  public $findMethods = array('active' => true);

  protected function _findActive($state, $query, $results = array()) {
    if ($state === 'before') {
      if (!isset($query['conditions'])) {
        $query['conditions'] = array();
      }
      $query['conditions'] = array_merge($query['conditions'], array(
        'Spotlight.published' => 1,
        'Spotlight.expiry_date >=' => date('Y-m-d')
      ));
      $query['fields'] = array('Spotlight.id', 'Spotlight.name', 'Spotlight.slug');
      $query['order'] = array('Spotlight.order' => 'ASC');
      $query['recursive'] = -1;
      return $query;
    }
    return $results;
  }

}

?>
