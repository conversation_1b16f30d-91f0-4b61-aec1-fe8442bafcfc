<?php

class Page extends AppModel {

  var $name = 'Page';

  var $actsAs = array('Inheritable', 'TreeCounterCache', 'Publishable');

  var $displayField = 'internal_ref';

  var $validate = array(
    'slug' => array(
      'format' => array(
        'rule' => array('custom', '/^[a-z0-9_]*$/i'),
        'message' => 'Slug can only contain alpha numeric characters and the underscore',
      ),
      'unique' => array(
        'rule' => 'isUnique',
        'message' => 'There is already a record with this slug - slugs must be unique',
      )
    )
  );

  var $belongsTo = array(
    'Image' => array(
      'className' => 'Image',
      'foreignKey' => 'image_id',
      'conditions' => '',
      'fields' => '',
      'order' => '',
      'list' => false,
      'form' => 'image_picker'
    )
  );

  var $hasMany = array(
    'ContentBlock' => array(
      'className' => 'ContentBlock',
      'foreignKey' => 'modelid',
      'dependent' => true,
      'conditions' => array('ContentBlock.model' => 'Page'),
      'fields' => '',
      'order' => '`order`',
      'limit' => '',
      'offset' => '',
      'exclusive' => '',
      'finderQuery' => '',
      'counterQuery' => '',
      'manageInEditPage' => true
    )
  );

  public $findMethods = array('holidayInfo' => true, 'about' => true);

  protected function _findHolidayInfo($state, $query, $results = array()) {
    // error_log("_findHolidayInfo called with state: " . $state);
    // error_log("Query: " . print_r($query, true));
    if ($state === 'before') {
      $query['conditions'] = array(
        'Page.published' => 1,
        'Page.css_class' => 'holiday_information',
        'Page.internal_ref IN' => array(
          'The Bon Voyage Holiday Promise',
          'US Airlines',
          'Holiday Travel USA',
          'US and Canada visa and immigration information',
          'Foreign and Commonwealth Office Travel Advice',
          'Accessibility'
        )
      );
      $query['fields'] = array('Page.id', 'Page.internal_ref', 'Page.slug');
      $query['order'] = "FIELD(Page.internal_ref, 'The Bon Voyage Holiday Promise', 'US Airlines', 'Holiday Travel USA', 'US and Canada visa and immigration information', 'Foreign and Commonwealth Office Travel Advice', 'Accessibility')";
      $query['recursive'] = -1;
      // error_log("Modified query: " . print_r($query, true));
      return $query;
    }
    // error_log("Results: " . print_r($results, true));
    return $results;
  }

  protected function _findAbout($state, $query, $results = array()) {
    // error_log("_findAbout called with state: " . $state);
    // error_log("Query: " . print_r($query, true));
    if ($state === 'before') {
      $query['conditions'] = array(
        'Page.published' => 1,
        'Page.css_class' => 'about_bon_voyage',
        'Page.internal_ref IN' => array(
          'About Us',
          'Our Customers Say',
          'Fully Bonded for Your Protection',
          'Telephone Numbers',
          'Address and Registered Details',
          'Bon Voyage Feefo Rating',
          'Finding Us',
          'Careers',
          'Press Centre'
        )
      );
      $query['fields'] = array('Page.id', 'Page.internal_ref', 'Page.slug');
      $query['order'] = "FIELD(Page.internal_ref, 'About Us', 'Our Customers Say', 'Fully Bonded for Your Protection', 'Telephone Numbers', 'Address and Registered Details', 'Bon Voyage Feefo Rating', 'Finding Us', 'Careers', 'Press Centre')";
      $query['recursive'] = -1;
      // error_log("Modified query: " . print_r($query, true));
      return $query;
    }
    // error_log("Results: " . print_r($results, true));
    return $results;
  }

  function getBySlug($slug) {
    $data = $this->find('first', array(
      'conditions' => array(
        'slug' => $slug,
      ),
      'contain' => array(
        'Image',
        'ContentBlock' => 'Image',
      )
    ));
    return $data;
  }

}

?>
