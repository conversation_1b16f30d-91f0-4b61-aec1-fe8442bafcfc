
____________________________________________________________________________________________________________________________________________

diff --git a/app/views/elements/chrome/search.ctp b/app/views/elements/chrome/search.ctp
index 433ec4ff..d5b5c65d 100644
--- a/app/views/elements/chrome/search.ctp
+++ b/app/views/elements/chrome/search.ctp
@@ -1,4 +1,4 @@
-<button class="search-toggle" data-toggle="search"></button>
+<button class="search-toggle" data-toggle="search"><img src="/img/site/icons/search.svg" alt="Search" class="active"></button>
 
 <div class="primary-search <?php if (isset($modifier)) echo 'primary-search--' . $modifier ?>" id="search">
     <?php if (isset($heading) && $heading === true): ?>

____________________________________________________________________________________________________________________________________________

diff --git a/app/webroot/js/site/src/site.js b/app/webroot/js/site/src/site.js
index 4fa2935a..d84e702a 100644
--- a/app/webroot/js/site/src/site.js
+++ b/app/webroot/js/site/src/site.js
@@ -674,7 +674,7 @@ function ready(){
       grecaptcha.execute('6LenjsgZAAAAAH-f4aj1WPD8Rflsj57FOaAcvtkU', { action: 'contact_form' }).then(function(token) {
         let model = 'QuoteRequest';
 
-        let travelPlanUrls = ['travel_plans', 'start_planning_now', 'make_an_enquiry'];
+        let travelPlanUrls = ['travel_plans', 'start_planning_now', 'make_an_enquiry','social'];
         let isTravelPlanUrl = false;
         travelPlanUrls.each(url => {
           if (location.pathname.includes(url)) {

____________________________________________________________________________________________________________________________________________

diff --git a/app/webroot/js/site/src/navigation.js b/app/webroot/js/site/src/navigation.js
index 53a97495..6e5085f1 100644
--- a/app/webroot/js/site/src/navigation.js
+++ b/app/webroot/js/site/src/navigation.js
@@ -19,11 +19,28 @@ App.Navigation = (function () {
     Navigation.prototype.toggle = function (e) {
         e.preventDefault();
 
-        $(e.target).toggleClassName('active');
-
-        var target = $(e.target).readAttribute('data-toggle');
-
-        $(target).toggleClassName('active');
+        // Find the toggle element - either the target itself or its parent button
+        var toggleElement = e.target;
+
+        // If the target doesn't have data-toggle, try to find a parent that does
+        if (!toggleElement.hasAttribute('data-toggle')) {
+            // Check if the target is inside a button with data-toggle
+            var parentButton = toggleElement.up('[data-toggle]');
+            if (parentButton) {
+                toggleElement = parentButton;
+            }
+        }
+
+        // Toggle active class on the toggle element
+        $(toggleElement).toggleClassName('active');
+
+        // Get the target from the data-toggle attribute
+        var target = $(toggleElement).readAttribute('data-toggle');
+
+        // Toggle active class on the target element
+        if (target) {
+            $(target).toggleClassName('active');
+        }
     };
 
     return Navigation;
