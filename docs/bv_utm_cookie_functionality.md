# BV UTM Cookie Functionality

## Overview

The `bv_utm` cookie system is designed to capture and persist UTM (Urchin Tracking Module) parameters from URL query strings, allowing the website to track marketing campaign effectiveness across user sessions and form submissions. This system ensures that UTM data is preserved even when users navigate between pages or return to the site later.

## How It Works

### 1. UTM Parameter Detection and Cookie Setting

When a user visits any page on the website with UTM parameters in the URL, the system automatically:

1. **Parses UTM Parameters**: The JavaScript function `getUTMParams()` in `app/webroot/js/tweaks.js` scans the URL for the following UTM parameters:
   - `utm_source`
   - `utm_medium` 
   - `utm_campaign`
   - `utm_term`
   - `utm_content`

2. **Sanitizes Parameters**: The `sanitizeAndEncodeUTMParams()` function:
   - Converts values to strings
   - Filters out invalid characters using regex: `/^[a-zA-Z0-9_\%\-\+&,\[\]\(\): ]+$/`
   - URL-encodes the sanitized values using `encodeURIComponent()`

3. **Sets Cookie**: If UTM parameters are found AND the `bv_utm` cookie doesn't already exist:
   - Converts the sanitized parameters to a URL-encoded string format
   - Sets the `bv_utm` cookie with a 7-day expiration
   - Uses secure flag when on HTTPS
   - Sets path to root (`/`)

**Example URL**: `https://example.com/page?utm_source=google&utm_medium=cpc&utm_campaign=summer2024`

**Resulting Cookie Value**: `utm_source=google&utm_medium=cpc&utm_campaign=summer2024`

### 2. Form Population

When any page loads, the system automatically:

1. **Checks for Cookie**: Looks for the `bv_utm` cookie
2. **Finds Form Fields**: Searches for hidden input fields with names containing `[utm]`
3. **Populates Fields**: Sets the cookie value into the hidden field

The selector used is: `input[name*="[utm]"]`

### 3. Form Submission and Data Flow

#### Forms That Include UTM Data

The following forms include hidden UTM fields that get populated:

- **Contact Form** (`app/views/contacts/contact_form.ctp`)
- **Quote Request Form** (`app/views/quote_requests/add.ctp`)  
- **Travel Plan Forms** (`app/views/travel_plans/add.ctp`, `app/views/travel_plans/add_lite.ctp`)
- **Subscription Form** (`app/views/subscriptions/index.ctp`)

Each form includes: `<?php echo $form->input('utm', ['type'=>'hidden','value' => '']); ?>`

#### Backend Processing

When forms are submitted, the UTM data flows through the following process:

**Contact Form** (`app/controllers/contacts_controller.php`):
```php
'UTM' => isset($this->data['Contact']['utm']) ? $this->data['Contact']['utm'] : '',
```

**Quote Request Form** (`app/controllers/quote_requests_controller.php`):
```php
'UTM' => $this->data['QuoteRequest']['utm'],
```

**Travel Plan Form** (`app/controllers/travel_plans_controller.php`):
- UTM data is stored in the database with the travel plan record
- Sent to external sales portal API

#### External API Integration

The UTM data is sent to external web services:

- **Contact Requests**: `AddWebContact.asmx/AddContactRequest_UTM`
- **Quote Requests**: `AddWebContact.asmx/AddQuoteRequest_UTM`

## Cookie Behavior

### Cookie Properties
- **Name**: `bv_utm`
- **Expiration**: 7 days from creation
- **Path**: `/` (site-wide)
- **Secure**: Only on HTTPS connections
- **Domain**: Current domain

### Cookie Persistence Rules
- **First Visit**: Cookie is set only if UTM parameters are present in URL AND cookie doesn't already exist
- **Subsequent Visits**: Existing cookie is preserved (not overwritten)
- **Expiration**: Cookie expires after 7 days, then can be set again on next UTM visit

### Cookie Value Format
The cookie stores UTM parameters as a URL-encoded query string:
```
utm_source=value1&utm_medium=value2&utm_campaign=value3
```

## Security Considerations

### Input Sanitization
- Character filtering removes potentially malicious characters
- Only alphanumeric characters, underscores, hyphens, plus signs, ampersands, brackets, parentheses, colons, spaces, and percent signs are allowed
- All values are URL-encoded before storage

### Data Validation
- UTM parameters are validated against a strict regex pattern
- Empty or invalid parameters are filtered out
- Cookie values are sanitized before being used in forms

## Integration with Legacy Systems

### Session-Based WebSource Tracking
The system also maintains backward compatibility with legacy UTM tracking via session variables in `app/app_controller.php`:

- Checks for `utm_source` parameters starting with 'ENews_'
- Sets session variable `WebSource` for tracking
- Integrates with Google Ads tracking via `gclid` parameter

### Google Analytics Integration
- UTM data flows through to Google Analytics for campaign tracking
- Form submissions trigger specific GA events for conversion tracking

## Debugging and Monitoring

### Console Logging
The system includes console logging for debugging:
- "Setting cookie" when UTM cookie is created
- "Getting cookie" when cookie is read
- "Hidden field found" when form field is populated

### Testing UTM Functionality
1. Visit site with UTM parameters: `?utm_source=test&utm_medium=email&utm_campaign=debug`
2. Check browser developer tools for console logs
3. Inspect cookie in browser storage
4. Submit a form and verify UTM data in backend logs

## File Locations

### JavaScript Files
- **Main UTM Logic**: `app/webroot/js/tweaks.js`
- **Form Handling**: `app/webroot/js/site/site.js`

### PHP Controllers
- **Contact Forms**: `app/controllers/contacts_controller.php`
- **Quote Requests**: `app/controllers/quote_requests_controller.php`
- **Travel Plans**: `app/controllers/travel_plans_controller.php`
- **Subscriptions**: `app/controllers/subscriptions_controller.php`

### Templates
- **Contact Form**: `app/views/contacts/contact_form.ctp`
- **Quote Request**: `app/views/quote_requests/add.ctp`
- **Travel Plans**: `app/views/travel_plans/add.ctp`, `app/views/travel_plans/add_lite.ctp`
- **Subscriptions**: `app/views/subscriptions/index.ctp`

## Summary

The `bv_utm` cookie system provides a robust, secure method for tracking marketing campaign effectiveness by:

1. Automatically capturing UTM parameters from URLs
2. Storing them securely in a 7-day cookie
3. Automatically populating form fields with the data
4. Sending UTM information to external APIs for campaign analysis
5. Maintaining backward compatibility with existing tracking systems

This system ensures that marketing attribution data is preserved throughout the user journey, providing valuable insights into campaign performance and user behavior.
